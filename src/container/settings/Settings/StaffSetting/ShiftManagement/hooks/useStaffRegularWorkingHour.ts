import { useSelector } from 'amos';
import { type Dayjs } from 'dayjs';
import { type WorkingHourValue } from '../../../../../../store/staff/staff.boxes';
import { selectBizSchedule } from '../../../../../../store/staffSchedule/staffSchedule.selectors';
import { type WeekTimeValue } from '../../../../../../store/staffSchedule/staffSchedule.types';
import { useCalcRotatingWeek } from './useCalcRotatingWeek';
import { staffScheduleWorkingHourMapBox } from '../../../../../../store/staffSchedule/staffSchedule.boxes';
import { getDefaultLimit } from '../components/Limitation/Limitation.type';

/**
 * Calculates a staff member's regular working hours for a given date.
 * Determines the rotating week and weekday, then returns the appropriate working hour configuration.
 *
 * @param staffId - The ID of the staff member
 * @param date - The date to calculate working hours for
 * @returns Working hour configuration including availability, time range, and limits
 */
export function useStaffRegularWorkingHour(staffId: number, date: Dayjs): WorkingHourValue {
  const [staffSchedule, bizSchedule] = useSelector(
    staffScheduleWorkingHourMapBox.mustGetItem(staffId),
    selectBizSchedule(),
  );
  const weekDayKey = date.format('dddd').toLowerCase() as keyof WeekTimeValue;
  const { weekKey } = useCalcRotatingWeek(staffId, date);
  const inCycleWeeks = staffSchedule.isWithInCycleWeek(date);

  if (!inCycleWeeks) {
    const timeRange = bizSchedule[weekDayKey] || [];

    return {
      isAvailable: timeRange.length > 0,
      timeRange,
      limit: getDefaultLimit(),
    };
  }

  return staffSchedule[weekKey][weekDayKey];
}
