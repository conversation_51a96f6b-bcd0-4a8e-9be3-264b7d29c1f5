import { useSelector } from 'amos';
import { type Dayjs } from 'dayjs';
import { type WorkingHourTimeRange } from '../../../../../../store/staff/staff.boxes';
import { selectBizSchedule } from '../../../../../../store/staffSchedule/staffSchedule.selectors';
import { type WeekTimeValue } from '../../../../../../store/staffSchedule/staffSchedule.types';
import { useCalcRotatingWeek } from './useCalcRotatingWeek';
import { staffScheduleWorkingHourMapBox } from '../../../../../../store/staffSchedule/staffSchedule.boxes';

/**
 * Gets the regular working time ranges for a staff member on a specific date.
 *
 * @param staffId - The ID of the staff member
 * @param date - The date to get working hours for
 * @returns Array of working hour time ranges for the staff member on the given date
 */
export function useStaffRegularTimes(staffId: number, date: Dayjs): WorkingHourTimeRange[] {
  const [staffSchedule, bizSchedule] = useSelector(
    staffScheduleWorkingHourMapBox.mustGetItem(staffId),
    selectBizSchedule(),
  );
  const weekDayKey = date.format('dddd').toLowerCase() as keyof WeekTimeValue;
  const { weekKey } = useCalcRotatingWeek(staffId, date);
  const inCycleWeeks = staffSchedule.isWithInCycleWeek(date);

  if (!inCycleWeeks) {
    return bizSchedule[weekDayKey] ?? [];
  }

  return staffSchedule[weekKey][weekDayKey]?.timeRange || [];
}
