import { type DependencyList, useEffect, useRef, useState } from 'react';
import { AvailabilityType } from '../../../../../../store/staffSchedule/staffSchedule.types';
import type { EnumValues } from '../../../../../../store/utils/createEnum';
import { StaffClient } from '../../../../../../middleware/clients';
import { useAsync, useUnmount } from 'react-use';

const DEFAULT_AVAILABILITY_TYPE = AvailabilityType.BY_TIME;

export interface AvailabilityTypeState {
  type: 'init' | 'changed';
  value: EnumValues<typeof AvailabilityType>;
}

export const useAvailabilityType = (viewType: number) => {
  const availabilityType = useRef(DEFAULT_AVAILABILITY_TYPE);

  // type 用于判断是否要从后端获取数据
  // 初始化时需要从后端获取数据，但用户编辑切换时，无需发送请求，直接使用本地数据
  const [draftAvailabilityType, setDraftAvailabilityType] = useState<AvailabilityTypeState>({
    value: DEFAULT_AVAILABILITY_TYPE,
    type: 'init',
  });

  /** GET */
  const getAvailabilityTypeAsync = async (businessId: string) => {
    const availability = await StaffClient.getBusinessStaffAvailabilityType({
      businessId: businessId,
    });
    availabilityType.current = availability.availabilityType;
    setDraftAvailabilityType({
      value: availability.availabilityType,
      type: 'init',
    });
  };

  /** PUT */
  const updateAvailabilityType = async (businessId: string) => {
    const prev = availabilityType.current;
    availabilityType.current = draftAvailabilityType.value;
    try {
      await StaffClient.updateBusinessStaffAvailabilityType({
        businessId: businessId,
        availabilityType: draftAvailabilityType.value,
      });
    } catch (error) {
      availabilityType.current = prev;
      setDraftAvailabilityType({
        type: 'init',
        value: prev,
      });
      throw error;
    }
  };

  /** 重置为初始值 */
  const resetAvailabilityType = async () => {
    setDraftAvailabilityType({
      value: availabilityType.current,
      type: 'init',
    });
  };

  useEffect(() => {
    setDraftAvailabilityType((prev) => ({
      ...prev,
      type: 'init',
    }));
  }, [viewType]);

  useUnmount(() => {
    resetAvailabilityType();
  });

  return {
    availabilityType,
    draftAvailabilityType,
    setDraftAvailabilityType,
    getAvailabilityTypeAsync,
    updateAvailabilityType,
    resetAvailabilityType,
  };
};

/**
 * availabilityType 第一次切换时，执行 fn
 *
 * 1. 如果一次性获取 by slot 和 by time 的数据，可能导致接口压力过大，而且用户可能并不需要同时获取这两份数据
 * 2. 前端需要做草稿态计算，所以不能每次都获取新数据，只在第一次切换 availabilityType 时才获取新数据
 *
 * @params availabilityType
 */
export const useAvailabilityTypeFirstSwitch = (
  availabilityType: AvailabilityTypeState,
  fn: () => void | Promise<void>,
  deps: DependencyList,
) => {
  const prevAvailabilityType = useRef(availabilityType);

  return useAsync(async () => {
    if (prevAvailabilityType.current.type === 'changed' && availabilityType.type === 'changed') return;
    prevAvailabilityType.current = availabilityType;

    await fn();
  }, [...deps, availabilityType.type, availabilityType.value]);
};
