import { useSelector } from 'amos';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { staffScheduleDateOverrideMapBox } from '../../../../../../store/staffSchedule/staffSchedule.boxes';
import { DateOverrideType } from '../../../../../../store/staffSchedule/staffSchedule.types';
import { useStaffRegularWorkingHour } from './useStaffRegularWorkingHour';

// 通过 staffId 和 date 获取当天的 working hour
export const useWorkingHourByDate = (staffId: number, date: string, type: DateOverrideType) => {
  const [record] = useSelector(staffScheduleDateOverrideMapBox.mustGetItem(staffId));
  const regular = useStaffRegularWorkingHour(staffId, dayjs(date));

  return useMemo(() => {
    const list = type === DateOverrideType.History ? record.history : record.ongoing;
    return list.find((i) => i.overrideDate === date)?.value || regular;
  }, [date, record.history, record.ongoing, regular, type]);
};
