import { useDispatch, useSelector } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import { useCallback, useEffect, useMemo } from 'react';
import { useGetServiceAreaList } from '../../../../../../components/ServiceArea/hooks/useGetServiceAreaList';
import { type ExtraCopyProps } from '../../../../../../components/WeekTimeScheduleShiftManagement/WeekTimeSchedule';
import { selectBusiness, selectCurrentBusiness } from '../../../../../../store/business/business.selectors';
import {
  type FullWeekDay,
  FullWeekDayList,
} from '../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { getBusinessServiceArea } from '../../../../../../store/serviceArea/serviceArea.actions';
import { selectBusinessServiceAreaIdList } from '../../../../../../store/serviceArea/serviceArea.selectors';
import { type ServiceAreaRange } from '../../../../../../store/staff/staff.boxes';
import { updateStaffServiceArea } from '../../../../../../store/staffSchedule/staffSchedule.actions';
import { staffScheduleAreaOverrideMapBox } from '../../../../../../store/staffSchedule/staffSchedule.boxes';
import { selectStaffScheduleServiceArea } from '../../../../../../store/staffSchedule/staffSchedule.selectors';
import {
  AvailabilityType,
  DateOverrideType,
  type RotateWeekKeyType,
  type WeekServiceAreaValue,
  type WeekTimeValue,
} from '../../../../../../store/staffSchedule/staffSchedule.types';
import { isNormal } from '../../../../../../store/utils/identifier';
import { useSerialCallback } from '../../../../../../utils/hooks/useSerialCallback';
import { useCalcRotatingWeek } from './useCalcRotatingWeek';

export const dayToWeekDay = (day: number) => {
  return FullWeekDayList[day].toLowerCase() as keyof WeekServiceAreaValue;
};

// 获取当前business的serviceArea list
export function useServiceArea(businessId: number) {
  const [business, businessServiceAreaIdList] = useSelector(
    selectBusiness(businessId),
    selectBusinessServiceAreaIdList(businessId),
  );

  const dispatch = useDispatch();
  const getData = useSerialCallback(async () => {
    await dispatch(getBusinessServiceArea(businessId));
  });

  useEffect(() => {
    // businessServiceAreaIdList 不需要作为dep
    if (isNormal(business.id) && business.isMobileGrooming() && !businessServiceAreaIdList.size) {
      getData();
    }
  }, [business]);
}

// 更新serviceArea
export function useUpdateServiceArea() {
  const [business, serviceAreaSchedule] = useSelector(selectCurrentBusiness(), selectStaffScheduleServiceArea());
  const dispatch = useDispatch();

  const update = (
    staffId: number,
    extra: { day: number; newVal: ServiceAreaRange[] },
    weekKey: RotateWeekKeyType = 'firstWeek',
  ) => {
    if (!business.isMobileGrooming()) return;

    const { day, newVal } = extra;
    const dayOfWeek = dayToWeekDay(day);
    const prevValue = serviceAreaSchedule[weekKey];

    dispatch(
      updateStaffServiceArea(staffId, {
        [weekKey]: {
          ...prevValue,
          [dayOfWeek]: newVal,
        },
      }),
    );
  };

  const copy = (staffId: number, extra: ExtraCopyProps, weekKey: RotateWeekKeyType = 'firstWeek') => {
    if (!business.isMobileGrooming()) return;

    const { day, days } = extra;
    const lowerDays = days.map((i) => i.toLowerCase() as Lowercase<FullWeekDay>);
    const dayOfWeek = dayToWeekDay(day);
    const currentWeekData = serviceAreaSchedule[weekKey];
    const targetValue = currentWeekData[dayOfWeek];
    const newValues = lowerDays.reduce(
      (pre, d) => ({
        ...pre,
        [d]: targetValue,
      }),
      {},
    );

    return dispatch(updateStaffServiceArea(staffId, { [weekKey]: { ...currentWeekData, ...newValues } }));
  };

  return { update, copy };
}

// 通过staffId和date获取当天的 serviceArea override 信息
export const useGetServiceAreaOverride = (staffId: number, date: string, type: DateOverrideType) => {
  const [business, record] = useSelector(selectCurrentBusiness(), staffScheduleAreaOverrideMapBox.mustGetItem(staffId));

  return useMemo(() => {
    if (!business.isMobileGrooming()) return;

    const list = type === DateOverrideType.History ? record.history : record.ongoing;
    return list.find((i) => i.overrideDate === date)?.workingArea;
  }, [business, type, record.history, record.ongoing, date]);
};

/** 根据日期获取当前 staff 的 serviceArea */
export function useStaffRegularServiceArea(staffId: number, date: Dayjs) {
  const [staffScheduleServiceArea] = useSelector(selectStaffScheduleServiceArea(staffId));
  const { weekKey } = useCalcRotatingWeek(staffId, date, AvailabilityType.BY_TIME);

  const weekDayKey = date.format('dddd').toLowerCase() as keyof WeekTimeValue;
  const inCycleWeeks = staffScheduleServiceArea.isWithInCycleWeek(date);
  const area = inCycleWeeks ? staffScheduleServiceArea[weekKey][weekDayKey] : undefined;

  return area;
}

/** 获取当前 staff 的 service area range，包含 override */
export const useGetServiceAreaRangeByDate = (staffId: number, date: string, type: DateOverrideType) => {
  const [business] = useSelector(selectCurrentBusiness());

  const serviceAreaOverride = useGetServiceAreaOverride(staffId, date, type);
  const serviceAreaRegular = useStaffRegularServiceArea(staffId, dayjs(date));

  return useMemo(() => {
    if (!business.isMobileGrooming()) return;
    return serviceAreaOverride || serviceAreaRegular;
  }, [serviceAreaOverride, serviceAreaRegular, business]);
};

// 通过id获取serviceArea info
export const useGetServiceAreaInfoById = (businessId: number) => {
  const [business] = useSelector(selectBusiness(businessId));
  const areaList = useGetServiceAreaList(businessId);

  return useCallback(
    (id: number) => {
      if (!business.isMobileGrooming()) return;
      return areaList.find((i) => i.areaId === id);
    },
    [areaList, business],
  );
};
