import dayjs, { type Dayjs } from 'dayjs';
import { RotateWeek, RotateWeekList } from '../../../../../../store/staffSchedule/staffSchedule.types';
import { isWithInCycleWeek } from '../../../../../../store/staffSchedule/staffSchedule.boxes';
import {
  MaxStaffEndDate,
  type StaffScheduleType,
  type WeekTimeValue,
  type RotateWeekKeyType,
} from '../../../../../../store/staffSchedule/staffSchedule.types';
import type { WorkingSlotValue, WorkingHourValue } from '../../../../../../store/staff/staff.boxes';
import type { EnumValues } from '../../../../../../store/utils/createEnum';

export interface RotateMetaData {
  weekNum: number;
  weekKey: RotateWeekKeyType;
}

export interface RotateMetaParams {
  date?: Dayjs;
  startDate?: Dayjs;
  endDate?: Dayjs;
  scheduleType: EnumValues<typeof StaffScheduleType>;
}

/**
 * 从 startDate 开始计算，计算当前日期是第几周
 * @param param0.date default today
 * @param param0.scheduleType 周期类型，1-4 周
 * @returns
 */
export function getRotatingWeekMeta({
  date = dayjs(),
  startDate = dayjs(),
  endDate = MaxStaffEndDate,
  scheduleType,
}: RotateMetaParams) {
  const currentDay = date.startOf('date');
  const dayKey = currentDay.format('dddd').toLowerCase() as keyof WeekTimeValue;
  const startWeek = startDate.startOf('week');
  const endWeek = endDate.startOf('week');

  if (!isWithInCycleWeek(currentDay, startWeek, endWeek, scheduleType)) {
    return {
      dayKey,
      weekNum: 0,
      weekKey: RotateWeek.firstWeek,
    };
  }

  const currentWeek = currentDay.startOf('week');
  const diffWeeks = currentWeek.diff(startWeek, 'week');
  const weekNum = diffWeeks % scheduleType;
  const weekKey = RotateWeekList[weekNum];

  return {
    dayKey,
    weekNum,
    weekKey,
  };
}

function isWorkingHourValue(value: WorkingHourValue | WorkingSlotValue): value is WorkingHourValue {
  return 'timeRange' in value;
}

/**
 * 把工作时间转化为范围
 * @param workingData
 * @returns
 */
export function getWeekDayTimeRange(workingData: WorkingHourValue | WorkingSlotValue) {
  if (isWorkingHourValue(workingData)) {
    return workingData.timeRange;
  } else {
    return [
      {
        startTime: workingData.slotDailySetting.startTime,
        endTime: workingData.slotDailySetting.endTime,
      },
    ];
  }
}
