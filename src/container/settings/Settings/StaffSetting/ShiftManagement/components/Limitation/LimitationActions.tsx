import React from 'react';
import { Button } from '@moego/ui';
import { LimitationRow } from './LimitationRow';
import type { LimitationFormValue } from './LimitationFormContext';
import { MinorEditOutlined } from '@moego/icons-react';
import { getDefaultLimit } from './Limitation.type';
import cn from 'classnames';
import { LimitationEditDrawer } from '../StaffSchedule/WorkingHourV2/LimitationEditDrawer';
import { useBool } from '../../../../../../../utils/hooks/useBool';

export interface AddLimitationButtonProps {
  className?: string;
  limit?: LimitationFormValue;
  isDisabled?: boolean;
  onChange?: (limit: LimitationFormValue) => void;
}

export const LimitationActions = (props: AddLimitationButtonProps) => {
  const { className, limit = getDefaultLimit(), onChange, isDisabled } = props;
  const hasLimit = Object.keys(limit || {}).some((key) => !!limit[key as keyof LimitationFormValue].length);
  const limitationDrawerVisible = useBool();

  return (
    <div className={className}>
      <LimitationRow limit={limit} />
      <Button
        isDisabled={isDisabled}
        icon={<MinorEditOutlined />}
        variant="tertiary"
        size="s"
        className={cn('moe-text-primary', {
          'moe-opacity-50': isDisabled,
        })}
        onPress={limitationDrawerVisible.open}
      >
        {hasLimit ? 'Edit' : 'Add'} limit
      </Button>
      <LimitationEditDrawer
        value={limit}
        visible={limitationDrawerVisible.value}
        onClose={limitationDrawerVisible.close}
        onConfirm={onChange}
      />
    </div>
  );
};
