import React from 'react';
import cn from 'classnames';
import { Tag, type TagProps, Text, Tooltip, type TooltipProps } from '@moego/ui';
import { useSelector, useStore } from 'amos';
import { selectPetSizeOptions } from '../../../../../../../store/onlineBooking/settings/petSize.selectors';
import { selectPetBreedOptions } from '../../../../../../../store/pet/petBreed.selectors';
import { selectPetTypeOptions } from '../../../../../../../store/pet/petType.selectors';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { MinorCircleFilled } from '@moego/icons-react';
import { type ServiceLimit, type PetBreedLimit, type PetSizeLimit, getDefaultLimit } from './Limitation.type';
import { selectServiceOptions } from '../../../../../../../store/service/service.selectors';
import { ServiceType } from '../../../../../../../store/service/category.boxes';
import type { LimitationFormValue } from './LimitationFormContext';
import { isNil } from 'lodash';
import { useValidLimitationOptions } from './hooks/useValidLimitationOptions';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';

interface LimitationRowProps {
  className?: string;
  limit?: LimitationFormValue;
  serviceItemType?: ServiceItemType;
}

/** 根据 limit 渲染 limitation 标签 */
export const LimitationRow = (props: LimitationRowProps) => {
  const { className, serviceItemType = ServiceItemType.GROOMING } = props;
  const store = useStore();
  const limit = useValidLimitationOptions({ value: props.limit || getDefaultLimit(), serviceItemType });
  const hasPetSizeLimit = limit.petSizeLimits.length > 0;
  const hasPetBreedLimit = limit.petBreedLimits.length > 0;
  const hasServiceLimit = limit.serviceLimits.length > 0;
  const isEmptyLimit = !hasPetSizeLimit && !hasPetBreedLimit && !hasServiceLimit;

  const [petSizeOptions, petTypeOptions, serviceOptions] = useSelector(
    selectPetSizeOptions(),
    selectPetTypeOptions(),
    selectServiceOptions(ServiceType.Service),
  );

  const getPetSizeTagLabel = ({ petSizeIds, isAllSize, capacity }: PetSizeLimit) => {
    const limitNumberLabel = capacity === 0 ? capacity : `Max ${capacity}`;

    const sizeLabels = isAllSize
      ? 'All sizes'
      : petSizeIds
          .map((id) => petSizeOptions.find(({ value }) => +id === +value)?.label)
          .filter(Boolean)
          .join(', ');

    return (
      <>
        <span className="moe-font-bold">{`${limitNumberLabel} `}</span>
        <span className="moe-font-regular">{sizeLabels}</span>
      </>
    );
  };

  const getPetBreedTagLabel = ({ petTypeId, isAllBreed, breedIds, capacity }: PetBreedLimit) => {
    const limitNumberLabel = capacity === 0 ? capacity : `Max ${capacity}`;
    if (!isNormal(petTypeId)) {
      return 'Unknown breed type';
    }

    const breedTypeOptions = store.select(selectPetBreedOptions(+petTypeId));

    const petTypeLabel = petTypeId ? petTypeOptions.find(({ value }) => +petTypeId == +value)?.label : 'unknown type';
    const breedLabels = isAllBreed
      ? 'All breeds'
      : breedIds
          .map((id) => breedTypeOptions.find(({ value }) => +id === +value)?.label)
          .filter(Boolean)
          .join(', ');

    return (
      <>
        <span className="moe-font-bold">{`${limitNumberLabel} `}</span>
        <span className="moe-font-regular">{`${petTypeLabel} - ${breedLabels}`}</span>
      </>
    );
  };

  const getServiceTagLabel = ({ capacity, isAllService, serviceIds }: ServiceLimit) => {
    const limitNumberLabel = capacity === 0 ? capacity : `Max ${capacity}`;

    const serviceLabels = isAllService
      ? ['All services']
      : serviceIds
          .map((id) => serviceOptions.find(({ value }) => id === value)?.label)
          .filter(Boolean)
          .join(', ');

    return (
      <>
        <span className="moe-font-bold">{`${limitNumberLabel} `}</span>
        <span className="moe-font-regular">{`${serviceLabels}`}</span>
      </>
    );
  };

  const getPetSizeTooltipProps = (limit: PetSizeLimit) => {
    const isReject = limit.capacity === 0;

    const labelList = limit.isAllSize
      ? ['All sizes']
      : limit.petSizeIds.map((id) => petSizeOptions.find(({ value }) => +id === +value)?.label || 'Unknown size');

    return {
      title: `Pet size limit ${isReject ? '(Reject)' : '(Max ' + limit.capacity + ')'}`,
      labelList,
    };
  };

  const getBreedTooltipProps = (limit: PetBreedLimit) => {
    if (!limit.petTypeId)
      return {
        title: null,
        labelList: [],
      };

    const isReject = limit.capacity === 0;
    const breedTypeOptions = store.select(selectPetBreedOptions(+limit.petTypeId));

    const breedName =
      petTypeOptions.find(({ value }) => !isNil(limit.petTypeId) && +limit.petTypeId === +value)?.label ||
      'Unknown pet type';
    const labelList = limit.isAllBreed
      ? ['All breeds']
      : limit.breedIds.map((id) => breedTypeOptions.find(({ value }) => +id === +value)?.label || 'Unknown breed');

    return {
      title: `${breedName} breeds limit ${isReject ? '(Reject)' : '(Max ' + limit.capacity + ')'}`,
      labelList,
    };
  };

  const getServiceTooltipProps = (limit: ServiceLimit) => {
    const isReject = limit.capacity === 0;
    const serviceOptions = store.select(selectServiceOptions(ServiceType.Service));

    const labelList = limit.isAllService
      ? ['All services']
      : limit.serviceIds.map((id) => serviceOptions.find(({ value }) => id === value)?.label || 'Unknown service');

    return {
      title: `Service limit ${isReject ? '(Reject)' : '(Max ' + limit.capacity + ')'}`,
      labelList,
    };
  };

  if (isEmptyLimit) return null;

  return (
    <div className={cn('moe-flex moe-gap-[12px]', className)}>
      <div className="moe-flex moe-gap-xs moe-flex-wrap">
        {limit.serviceLimits.map((limit, index) => (
          <LimitationTag key={index} tooltipProps={getServiceTooltipProps(limit)} label={getServiceTagLabel(limit)} />
        ))}
        {limit.petSizeLimits.map((limit, index) => (
          <LimitationTag key={index} tooltipProps={getPetSizeTooltipProps(limit)} label={getPetSizeTagLabel(limit)} />
        ))}
        {limit.petBreedLimits.map((limit, index) => (
          <LimitationTag key={index} tooltipProps={getBreedTooltipProps(limit)} label={getPetBreedTagLabel(limit)} />
        ))}
      </div>
    </div>
  );
};

type LimitationTagProps = TagProps & {
  tooltipProps: Omit<TooltipProps, 'content' | 'children'> & {
    title: string | null;
    labelList: string[];
  };
};

const LimitationTag = (props: LimitationTagProps) => {
  const { tooltipProps, ...tagProps } = props;
  const { title, labelList, ...restTooltipProps } = tooltipProps;

  return (
    <Tooltip
      side="top"
      backgroundTheme="light"
      content={
        title ? (
          <div>
            <Text variant="small">{title}</Text>
            <div>
              {labelList.map((label, index) => (
                <Text className="moe-flex moe-items-center moe-gap-xs" variant="small" key={index}>
                  <MinorCircleFilled className="moe-w-[6px] moe-h-[6px]" />
                  {label}
                </Text>
              ))}
            </div>
          </div>
        ) : null
      }
      {...restTooltipProps}
    >
      <Tag className="moe-max-w-[240px] moe-cursor-pointer" color="neutral" variant="filled" {...tagProps} />
    </Tooltip>
  );
};

LimitationRow.displayName = 'LimitationRow';
