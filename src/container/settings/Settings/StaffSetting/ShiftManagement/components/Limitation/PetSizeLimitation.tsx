import { MinorErrorFilled, MinorPlusOutlined, MinorTrashOutlined } from '@moego/icons-react';
import {
  Button,
  Checkbox,
  Controller,
  Form,
  IconButton,
  Input,
  Overflow,
  Select,
  Tag,
  Text,
  useFieldArray,
} from '@moego/ui';
import { useSelector } from 'amos';
import React, { isValidElement, useContext, useMemo } from 'react';
import {
  MINIMUM_PET_SIZE,
  selectPetSizeOptions,
} from '../../../../../../../store/onlineBooking/settings/petSize.selectors';
import type { PetSizeLimit } from './Limitation.type';
import { difference } from 'lodash';
import { LimitationFormContext } from './LimitationFormContext';
import { StaffScheduleTestIds } from '../../../../../../../config/testIds/staffSchedule';
import { LimitationMaxMountFormLabel } from './LimitationMaxMountFormLabel';

const defaultPetSizeLimit: PetSizeLimit = {
  petSizeIds: [],
  isAllSize: false,
  capacity: null,
};

const errorKeys: (keyof PetSizeLimit)[] = ['petSizeIds', 'capacity'];

export const PetSizeLimitation = () => {
  const { form } = useContext(LimitationFormContext);
  const value = form?.watch('petSizeLimits');
  const errors = form?.formState?.errors?.petSizeLimits;

  const { fields, append, remove } = useFieldArray({
    control: form?.control,
    name: 'petSizeLimits',
  });

  const [options] = useSelector(selectPetSizeOptions());
  const hasLimit = !!value?.length;

  const petSizeOptions = useMemo(
    () =>
      options.map((option) => ({
        ...option,
        value: String(option.value),
      })),
    [options],
  );

  const onAddLimit = () => {
    append(defaultPetSizeLimit);
  };

  const onRemoveLimit = (index: number) => {
    remove(index);
  };

  const removeAll = () => {
    form?.setValue('petSizeLimits', []);
  };

  // 动态获取 select options
  const allCheckedKeys = value?.reduce?.((keys, item) => {
    return [...keys, ...(item.petSizeIds || [])];
  }, [] as string[]);

  const renderError = (index: number) => {
    const currentRowErrors = errors?.[index];
    if (!currentRowErrors) return null;
    const errorKey = errorKeys.find((key) => currentRowErrors[key]?.message);

    if (!errorKey) return null;

    return (
      <Text className="moe-text-danger moe-mt-xs moe-flex moe-gap-xxs moe-items-center" variant="small">
        <MinorErrorFilled />
        {currentRowErrors[errorKey]!.message}
      </Text>
    );
  };

  return (
    <div>
      <Checkbox
        isSelected={hasLimit}
        onChange={(isSelected) => {
          removeAll();
          if (isSelected) {
            append(defaultPetSizeLimit);
          }
        }}
      >
        Pet size limitation
      </Checkbox>

      {hasLimit && (
        <div className="moe-mt-[12px]">
          <div className="moe-py-xs moe-px-s moe-bg-neutral-sunken-light moe-rounded-s">
            {/* Header */}
            <div className="moe-grid moe-grid-cols-3 moe-gap-x-3" style={{ gridTemplateColumns: '1fr 126px 24px' }}>
              <Form.Label isRequired>Pet size</Form.Label>
              <LimitationMaxMountFormLabel />

              <div></div>
            </div>

            {/* Content */}
            {fields.map((item, index) => {
              return (
                <div key={item.id} className="moe-mb-xs last:moe-mb-0">
                  <div
                    className="moe-grid moe-grid-cols-3 moe-gap-x-3 moe-gap-y-xs"
                    style={{ gridTemplateColumns: '1fr 126px 24px' }}
                  >
                    <Controller
                      control={form?.control}
                      name={`petSizeLimits.${index}.petSizeIds`}
                      render={({ field }) => {
                        const disabledKeysWithoutCurrentValue = difference(allCheckedKeys, field.value);

                        return (
                          <Select.Multiple
                            showSelectAll
                            enableSelectAllSearch
                            mode="tag"
                            classNames={{
                              base: 'moe-overflow-hidden',
                              control: 'moe-overflow-hidden',
                            }}
                            tagClassNames={{
                              base: 'moe-max-w-[282px]',
                            }}
                            items={petSizeOptions}
                            disabledKeys={disabledKeysWithoutCurrentValue}
                            value={allCheckedKeys}
                            renderValues={(values, _, renderItems) => {
                              // renderItems 是组件库提供的渲染函数，默认情况下，组件库会使用 renderItems(values) 的返回值作为 tag children
                              // 自定义场景：tag 超出一行后使用 +x 表示剩余数量，并且不渲染 disabled tag
                              const valuesWithoutDisable = values.filter(
                                ({ key }) => !disabledKeysWithoutCurrentValue.includes(`${key}`),
                              );

                              const itemsElement = renderItems(valuesWithoutDisable);

                              if (valuesWithoutDisable.length > 0) {
                                const validItemsElement = isValidElement(itemsElement) ? (
                                  itemsElement
                                ) : (
                                  <>{itemsElement}</>
                                );

                                return (
                                  <Overflow
                                    key={`${index}-${values.length}`}
                                    className="moe-gap-xs moe-my-[-2px]"
                                    renderRemaining={(count) => (
                                      <Tag
                                        className="moe-shrink-0 moe-whitespace-nowrap"
                                        variant="filled"
                                        color="neutral"
                                        label={`+${count}`}
                                      />
                                    )}
                                  >
                                    {validItemsElement?.props?.children}
                                  </Overflow>
                                );
                              }

                              return itemsElement;
                            }}
                            onChange={(keys) => {
                              const nextKeys = difference(keys, disabledKeysWithoutCurrentValue).map((key) => `${key}`);
                              field.onChange(nextKeys);
                              form?.setValue(`petSizeLimits.${index}.isAllSize`, keys.length === options.length);
                            }}
                            data-testid={`${StaffScheduleTestIds.PetSizeLimitationPetSizeSelector}-${index}`}
                          />
                        );
                      }}
                    />

                    <Controller
                      control={form?.control}
                      name={`petSizeLimits.${index}.capacity`}
                      render={({ field }) => {
                        return (
                          <Input.Number
                            placeholder="Enter…"
                            precision={0}
                            minValue={MINIMUM_PET_SIZE}
                            value={field.value}
                            onChange={field.onChange}
                            data-testid={`${StaffScheduleTestIds.PetSizeLimitationPetCountInput}-${index}`}
                          />
                        );
                      }}
                    />

                    <IconButton
                      icon={<MinorTrashOutlined />}
                      color="transparent"
                      onPress={() => onRemoveLimit(index)}
                      data-testid={`${StaffScheduleTestIds.PetSizeLimitationDeleteBtn}-${index}`}
                    />
                  </div>
                  {renderError(index)}
                </div>
              );
            })}

            <Button size="s" variant="tertiary" icon={<MinorPlusOutlined />} onPress={onAddLimit}>
              Add limit
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

PetSizeLimitation.displayName = 'PetSizeLimitation';
