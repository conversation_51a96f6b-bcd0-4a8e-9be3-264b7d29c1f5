import { MinorErrorFilled, MinorPlusOutlined, MinorTrashOutlined } from '@moego/icons-react';
import {
  Button,
  Checkbox,
  Controller,
  Form,
  IconButton,
  Input,
  Overflow,
  Select,
  Tag,
  Text,
  useFieldArray,
} from '@moego/ui';
import { useSelector } from 'amos';
import React, { isValidElement, useContext } from 'react';
import { MINIMUM_PET_SIZE } from '../../../../../../../store/onlineBooking/settings/petSize.selectors';
import type { ServiceLimit } from './Limitation.type';
import { difference } from 'lodash';
import { LimitationFormContext } from './LimitationFormContext';
import { ServiceType } from '../../../../../../../store/service/category.boxes';
import { selectServiceList, selectServiceListWithCategory } from '../../../../../../../store/service/service.selectors';
import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { StaffScheduleTestIds } from '../../../../../../../config/testIds/staffSchedule';
import { LimitationMaxMountFormLabel } from './LimitationMaxMountFormLabel';

interface ServiceLimitationProps {
  serviceItemType?: ServiceItemType;
}

const defaultServiceLimit: ServiceLimit = {
  serviceIds: [],
  isAllService: false,
  capacity: null,
};

const errorKeys: (keyof ServiceLimit)[] = ['serviceIds', 'capacity'];

export const ServiceLimitation = (props: ServiceLimitationProps) => {
  const { serviceItemType = ServiceItemType.GROOMING } = props;
  const { form } = useContext(LimitationFormContext);
  const value = form?.watch('serviceLimits');
  const errors = form?.formState?.errors?.serviceLimits;

  const { fields, append, remove } = useFieldArray({
    control: form?.control,
    name: 'serviceLimits',
  });
  const [options, serviceIdList] = useSelector(
    selectServiceListWithCategory([ServiceType.Service, serviceItemType]),
    selectServiceList([ServiceType.Service, serviceItemType]),
  );
  const hasLimit = !!value?.length;

  const onAddLimit = () => {
    append(defaultServiceLimit);
  };

  const onRemoveLimit = (index: number) => {
    remove(index);
  };

  const removeAll = () => {
    form?.setValue('serviceLimits', []);
  };

  // 动态获取 select options
  const allCheckedKeys = value?.reduce((keys, item) => {
    return [...keys, ...(item.serviceIds || [])];
  }, [] as string[]);

  const renderError = (index: number) => {
    const currentRowErrors = errors?.[index];
    if (!currentRowErrors) return null;
    const errorKey = errorKeys.find((key) => currentRowErrors[key]?.message);

    if (!errorKey) return null;

    return (
      <Text className="moe-text-danger moe-mt-xs moe-flex moe-gap-xxs moe-items-center" variant="small">
        <MinorErrorFilled />
        {currentRowErrors[errorKey]!.message}
      </Text>
    );
  };

  return (
    <div>
      <Checkbox
        isSelected={hasLimit}
        onChange={(isSelected) => {
          removeAll();
          if (isSelected) {
            append(defaultServiceLimit);
          }
        }}
        data-testid={StaffScheduleTestIds.ServiceLimitationCheckbox}
      >
        Service limitation
      </Checkbox>

      {hasLimit && (
        <div className="moe-mt-[12px]">
          <div className="moe-py-xs moe-px-s moe-bg-neutral-sunken-light moe-rounded-s">
            {/* Header */}
            <div className="moe-grid moe-grid-cols-3 moe-gap-x-3" style={{ gridTemplateColumns: '1fr 126px 24px' }}>
              <Form.Label isRequired>Service</Form.Label>
              <LimitationMaxMountFormLabel />
              <div></div>
            </div>

            {/* Content */}
            {fields.map((item, index) => {
              return (
                <div key={item.id} className="moe-mb-xs last:moe-mb-0">
                  <div
                    className="moe-grid moe-grid-cols-3 moe-gap-x-3 moe-gap-y-xs"
                    style={{ gridTemplateColumns: '1fr 126px 24px' }}
                  >
                    <Controller
                      control={form?.control}
                      name={`serviceLimits.${index}.serviceIds`}
                      render={({ field }) => {
                        const disabledKeysWithoutCurrentValue = difference(allCheckedKeys, field.value);

                        return (
                          <Select.Multiple
                            showSelectAll
                            enableSelectAllSearch
                            mode="tag"
                            classNames={{
                              base: 'moe-overflow-hidden',
                              control: 'moe-overflow-hidden',
                            }}
                            items={options}
                            disabledKeys={disabledKeysWithoutCurrentValue}
                            value={allCheckedKeys}
                            renderValues={(values, _, renderItems) => {
                              // renderItems 是组件库提供的渲染函数，默认情况下，组件库会使用 renderItems(values) 的返回值作为 tag children
                              // 自定义场景：tag 超出一行后使用 +x 表示剩余数量，并且不渲染 disabled tag
                              const valuesWithoutDisable = values.filter(
                                ({ key }) => !disabledKeysWithoutCurrentValue.includes(`${key}`),
                              );
                              const itemsElement = renderItems(valuesWithoutDisable);

                              if (valuesWithoutDisable.length > 0) {
                                const validItemsElement = isValidElement(itemsElement) ? (
                                  itemsElement
                                ) : (
                                  <>{itemsElement}</>
                                );

                                return (
                                  <Overflow
                                    key={`${index}-${values.length}`}
                                    className="moe-gap-xs moe-my-[-2px]"
                                    renderRemaining={(count) => (
                                      <Tag
                                        className="moe-shrink-0 moe-whitespace-nowrap"
                                        variant="filled"
                                        color="neutral"
                                        label={`+${count}`}
                                      />
                                    )}
                                  >
                                    {validItemsElement?.props?.children}
                                  </Overflow>
                                );
                              }

                              return itemsElement;
                            }}
                            onChange={(keys) => {
                              const nextKeys = difference(keys, disabledKeysWithoutCurrentValue).map((key) => `${key}`);
                              field.onChange(nextKeys);
                              form?.setValue(`serviceLimits.${index}.isAllService`, keys.length === serviceIdList.size);
                            }}
                            data-testid={`${StaffScheduleTestIds.ServiceLimitationServiceSelector}-${index}`}
                          />
                        );
                      }}
                    />

                    <Controller
                      control={form?.control}
                      name={`serviceLimits.${index}.capacity`}
                      render={({ field }) => {
                        return (
                          <Input.Number
                            placeholder="Enter…"
                            precision={0}
                            minValue={MINIMUM_PET_SIZE}
                            value={field.value}
                            onChange={field.onChange}
                            data-testid={`${StaffScheduleTestIds.ServiceLimitationPetCountInput}-${index}`}
                          />
                        );
                      }}
                    />

                    <IconButton
                      icon={<MinorTrashOutlined />}
                      color="transparent"
                      onPress={() => onRemoveLimit(index)}
                      data-testid={`${StaffScheduleTestIds.ServiceLimitationDeleteBtn}-${index}`}
                    />
                  </div>
                  {renderError(index)}
                </div>
              );
            })}

            <Button size="s" variant="tertiary" icon={<MinorPlusOutlined />} onPress={onAddLimit}>
              Add limit
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

ServiceLimitation.displayName = 'ServiceLimitation';
