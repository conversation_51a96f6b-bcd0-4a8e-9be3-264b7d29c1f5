export interface PetBreedLimit {
  petTypeId: string | null;
  isAllBreed: boolean;
  breedIds: string[];
  capacity: number | null;
}

export interface PetSizeLimit {
  petSizeIds: string[];
  isAllSize: boolean;
  capacity: number | null;
}

export interface ServiceLimit {
  serviceIds: string[];
  isAllService: boolean;
  capacity: number | null;
}

export const getDefaultLimit = () => ({
  petSizeLimits: [],
  petBreedLimits: [],
  serviceLimits: [],
});

type FormValidValue<T> = {
  [K in keyof T]: NonNullable<T[K]>;
};

export type ValidPetBreedLimit = FormValidValue<PetBreedLimit>;
export type ValidPetSizeLimit = FormValidValue<PetSizeLimit>;
export type ValidServiceLimit = FormValidValue<ServiceLimit>;
