import React from 'react';
import { useSelector } from 'amos';
import { useDispatch } from 'amos';
import { staffMapBox } from '../../../../../../../store/staff/staff.boxes';
import { updateStaffScheduleWorkingSlot } from '../../../../../../../store/staffSchedule/staffSchedule.actions';
import { StaffScheduleBySlotView } from './StaffScheduleBySlotView';
import { usePricingPermissionAccess } from '../../../../../../../components/Pricing/pricing.hooks';
import { staffScheduleWorkingSlotMapBox } from '../../../../../../../store/staffSchedule/staffSchedule.boxes';

interface StaffScheduleBySlotProps {
  staffId: number;
  className?: string;
}

export const StaffScheduleBySlot = (props: StaffScheduleBySlotProps) => {
  const { staffId } = props;
  const dispatch = useDispatch();
  const [staff, staffScheduleWorkingSlot] = useSelector(
    staffMapBox.mustGetItem(staffId),
    staffScheduleWorkingSlotMapBox.mustGetItem(staffId),
  );

  const { access: hasBySlotAuth } = usePricingPermissionAccess('bookBySlot');

  return (
    <StaffScheduleBySlotView
      isDisabled={!hasBySlotAuth}
      staff={staff}
      value={staffScheduleWorkingSlot}
      scheduleType={staffScheduleWorkingSlot.scheduleType}
      dateScope={[staffScheduleWorkingSlot.startDate, staffScheduleWorkingSlot.endDate]}
      onChange={(v) => {
        dispatch(updateStaffScheduleWorkingSlot(staffId, v));
      }}
      onScheduleTypeChange={(v) => {
        dispatch(
          updateStaffScheduleWorkingSlot(staffId, {
            scheduleType: v,
          }),
        );
      }}
    />
  );
};

StaffScheduleBySlot.displayName = 'StaffScheduleBySlot';
