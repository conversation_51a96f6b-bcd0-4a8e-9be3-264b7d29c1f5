import { Drawer as <PERSON><PERSON><PERSON><PERSON>, Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { sleep } from 'monofile-utilities/lib/sleep';
import React, { useEffect, useRef, useState } from 'react';
import IconCrown from '../../../../../../../../assets/svg/pricing-icon-crown.svg';
import { Button } from '../../../../../../../../components/Button/Button';
import { SvgIcon } from '../../../../../../../../components/Icon/Icon';
import { useUpgradeGuideModal } from '../../../../../../../../components/UpgradeGuideModal/useUpgradeGuideModal';
import { selectBusiness } from '../../../../../../../../store/business/business.selectors';
import { getClosedDateList } from '../../../../../../../../store/business/closedDate.actions';
import { getHolidays } from '../../../../../../../../store/business/holiday.actions';
import {
  addStaffSlotOverride,
  getStaffSlotOverride,
  modifyStaffSlotOverride,
} from '../../../../../../../../store/staffSchedule/staffSchedule.actions';
import {
  AvailabilityType,
  type DateOverrideDrawerSlotValue,
} from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import { useSerialCallback } from '../../../../../../../../utils/hooks/useSerialCallback';
import { useToggleIntercom } from '../../../../../../../../utils/hooks/useToggleIntercom';
import { OverrideDrawerContentView, OverrideDrawerWrapView } from '../../../StaffSchedule.style';
import { useShiftManagementAuth } from '../../UpgradeIntercept';
import { EditingStaffProfile } from './EditingStaffProfile';
import { SimpleCalendar } from './SimpleCalendar';
import { OverrideWorkingSlot } from './OverrideWorkingSlot';
import { FullWeekDayList } from '../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { selectStaffAllScheduleSlotOverride } from '../../../../../../../../store/staffSchedule/staffSchedule.selectors';
import { useRotatingWeekFallbackBiz } from '../../../hooks/useCalcRotatingWeek';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../../../utils/DateTimeUtil';
import { getDefaultLimit } from '../../Limitation/Limitation.type';

export interface AddOverrideDrawerBySlotProps {
  staffId: number;
  visible?: boolean;
  onClose?: () => void;
  defaultDate?: string;
  value?: DateOverrideDrawerSlotValue;
  onChange?: (v: DateOverrideDrawerSlotValue) => void;
  businessId: number;
}

const defaultOverrideDrawerValue: DateOverrideDrawerSlotValue = {
  dates: undefined,
  value: undefined,
};

export function AddOverrideDrawerBySlot(props: AddOverrideDrawerBySlotProps) {
  const { visible, onClose, staffId, defaultDate, businessId, value } = props;
  const ref = useRef<HTMLDivElement>(null);
  const hasAuth = useShiftManagementAuth();
  const dispatch = useDispatch();
  const [business] = useSelector(selectBusiness(businessId));

  const [innerValue, setInnerValue] = useState<DateOverrideDrawerSlotValue>(value || defaultOverrideDrawerValue);

  useEffect(() => {
    if (!visible) {
      setInnerValue(defaultOverrideDrawerValue);
    }
  }, [visible]);

  useEffect(() => {
    setInnerValue(value || defaultOverrideDrawerValue);
  }, [value]);

  const { dates = [], value: slotValue } = innerValue;
  const isEdit = !!defaultDate; // 如果有传入defaultDate，说明是从某一天的日历点击进来的
  const hasSelectedDate = isEdit || dates.length > 0;
  const { renderUpgradeGuideModal, openUpgradeGuideModal } = useUpgradeGuideModal({
    permissionKey: 'rotatingSchedule',
  });

  const getDefaultTimeRange = useRotatingWeekFallbackBiz(staffId, AvailabilityType.BY_SLOT);
  const [overrides] = useSelector(selectStaffAllScheduleSlotOverride(staffId));

  useToggleIntercom(visible);

  const onConfirm = useSerialCallback(async () => {
    if (isEdit) {
      await dispatch(modifyStaffSlotOverride(defaultDate, staffId, innerValue));
    } else {
      await dispatch(addStaffSlotOverride(staffId, innerValue));
    }
    await dispatch(getStaffSlotOverride([staffId]));
    onClose?.();
  });

  useEffect(() => {
    if (visible) {
      dispatch([getHolidays(businessId), getClosedDateList(businessId)]);
    }
  }, [visible]);

  const renderNoAuthTip = () =>
    !hasAuth && (
      <>
        <div className="!moe-flex !moe-items-start !moe-p-[16px] !moe-rounded-[8px] !moe-border-solid !moe-border-[1px] !moe-border-[#E6E6E6] !moe-text-[#333]">
          <SvgIcon src={IconCrown} color="#FAAD14" size={14} className="!moe-mt-[3px]" />
          <div className="!moe-ml-[10px]">
            <p className="!moe-mb-[8px]">
              Unlock hassle-free scheduling experience with new shift management solution!
            </p>
            <Button
              btnType="secondary"
              buttonRadius="circle"
              className="!moe-py-[6px] !moe-px-[12px] !moe-font-bold !moe-text-[12px] !moe-leading-[20px] !moe-bg-brand-bold hover:!moe-bg-brand-hover hover:!moe-border-brand-hover !moe-shadow-none"
              onClick={() => {
                openUpgradeGuideModal();
              }}
            >
              Upgrade
            </Button>
          </div>
        </div>
        {renderUpgradeGuideModal({ style: { zIndex: 1050 } })}
      </>
    );

  const renderDate = (d: dayjs.Dayjs | string) => {
    return d ? `${business.formatDate(d)} ${FullWeekDayList[dayjs(d).get('day')]}` : '';
  };

  return (
    <MoeDrawer
      isDismissable={false}
      title={isEdit ? 'Edit date override' : 'Add date override'}
      isOpen={visible}
      onClose={onClose}
      confirmButtonProps={{
        isLoading: onConfirm.isBusy(),
        isDisabled: !hasSelectedDate || !hasAuth,
      }}
      confirmText="Save"
      onConfirm={onConfirm}
      className="moe-w-[600px]"
    >
      <Text variant="small">Date override updates the working hours and online booking availability.</Text>
      <OverrideDrawerWrapView className="!moe-h-full !moe-flex !moe-flex-col">
        <div className="!moe-flex !moe-flex-col !moe-flex-1 !moe-pb-[0]" ref={ref}>
          {hasAuth && <EditingStaffProfile staffId={staffId} className="moe-mt-[12px]" />}
          {renderNoAuthTip()}
          <OverrideDrawerContentView className={classNames('moe-flex moe-flex-col ', { disabled: !hasAuth })}>
            <SimpleCalendar
              staffId={staffId}
              isEdit={isEdit}
              defaultDate={dayjs(defaultDate)}
              dateFormatMD={(date) => date.format(business.dateFormatMD)}
              value={innerValue.dates}
              onChange={(nextDates) => {
                if (isEdit && nextDates.length === 0) {
                  return;
                }

                const first = nextDates?.[0];
                if (dates.length === 0 && !slotValue && first) {
                  const fmted = first.format(DATE_FORMAT_EXCHANGE);
                  const isExistingOverride = overrides.find((i) => i.overrideDate === fmted);
                  const defaultTimeRange = getDefaultTimeRange(first).currentTimeRange;
                  const value = isExistingOverride
                    ? isExistingOverride.value
                    : {
                        isAvailable: false,
                        slotDailySetting: {
                          startTime: defaultTimeRange[0].startTime,
                          endTime: defaultTimeRange[0].endTime,
                          capacity: 1,
                          isAvailable: true,
                          limit: getDefaultLimit(),
                        },
                        slotHourSettingList: [
                          {
                            startTime: defaultTimeRange[0].startTime,
                            capacity: 1,
                            limit: getDefaultLimit(),
                          },
                        ],
                      };
                  setInnerValue({
                    value,
                    dates: nextDates,
                  });
                } else {
                  setInnerValue((prev) => {
                    return {
                      ...prev,
                      dates: nextDates,
                    };
                  });
                }
              }}
              businessId={businessId}
            />
            {hasSelectedDate && (
              <OverrideWorkingSlot
                value={slotValue}
                onChange={(v) => setInnerValue((prev) => ({ ...prev, value: v }))}
                onAdd={async () => {
                  // after sync with dom, then scroll
                  await sleep(0);
                  if (ref.current) {
                    ref.current.scrollTo({
                      top: ref.current.scrollHeight,
                      behavior: 'smooth',
                    });
                  }
                }}
                emptyText={dates.length > 1 ? undefined : `Not working on ${renderDate(dates[0])}`}
              />
            )}
          </OverrideDrawerContentView>
        </div>
      </OverrideDrawerWrapView>
    </MoeDrawer>
  );
}
