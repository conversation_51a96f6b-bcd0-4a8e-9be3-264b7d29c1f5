import React, { useEffect, useState } from 'react';
import { Heading } from '@moego/ui';
import { EveryWeekTabs } from '../EveryWeekTabs';
import { ScheduleParams, type ScheduleParamsValue } from './ScheduleParams';
import { WeekSlotSchedule } from './WorkingSlot/WeekSlotSchedule';
import {
  StaffScheduleType,
  WeekKeyMapNum,
  RotateWeekList,
  MaxStaffEndDate,
  type WeekSlotValue,
  type RotateWeekKeyType,
} from '../../../../../../../store/staffSchedule/staffSchedule.types';
import dayjs, { type Dayjs } from 'dayjs';
import type { StaffRecord } from '../../../../../../../store/staff/staff.boxes';

export type StaffScheduleBySlotViewValue = {
  startDate: Dayjs;
  endDate: Dayjs;
} & {
  [K in RotateWeekKeyType]: WeekSlotValue;
};

export interface StaffScheduleBySlotViewProps {
  staff: StaffRecord;
  className?: string;
  scheduleType?: WeekKeyMapNum;
  value: StaffScheduleBySlotViewValue;
  // 用于计算 rotating week
  dateScope?: [Dayjs, Dayjs];
  onScheduleTypeChange?: (scheduleType: WeekKeyMapNum) => void;
  onChange?: (value: StaffScheduleBySlotViewValue) => void;
  isDisabled?: boolean;
}

const defaultDateScope: [Dayjs, Dayjs] = [dayjs(), MaxStaffEndDate];

export const StaffScheduleBySlotView = (props: StaffScheduleBySlotViewProps) => {
  const {
    className,
    scheduleType = StaffScheduleType.Every1Week,
    value,
    onChange,
    isDisabled,
    staff,
    dateScope = defaultDateScope,
    onScheduleTypeChange,
  } = props;
  const [activeWeekType, setActiveWeekType] = useState<WeekKeyMapNum>(WeekKeyMapNum.firstWeek);
  const activeWeekKey = RotateWeekList[activeWeekType - 1];

  const handleScheduleTypeChange = (v: ScheduleParamsValue) => {
    setActiveWeekType((prevActiveWeekType) => {
      const nextScheduleType = v.scheduleType;
      return prevActiveWeekType <= nextScheduleType ? prevActiveWeekType : WeekKeyMapNum.firstWeek;
    });
    onScheduleTypeChange?.(v.scheduleType);
  };

  useEffect(() => {
    setActiveWeekType((prevActiveWeekType) =>
      prevActiveWeekType <= scheduleType ? prevActiveWeekType : WeekKeyMapNum.firstWeek,
    );
  }, [scheduleType]);

  return (
    <div className={className}>
      <Heading size="5" className="moe-mb-2">
        {staff.firstName}&apos;s rotating frequency & Regular slot setting
      </Heading>
      <ScheduleParams
        value={{
          scheduleType,
          startDate: dateScope[0],
          endDate: dateScope[1],
        }}
        onChange={handleScheduleTypeChange}
      />
      <EveryWeekTabs
        scheduleType={scheduleType}
        dateScope={[value.startDate, value.endDate]}
        className="moe-border-divider moe-border moe-rounded-m moe-py-s moe-px-5 moe-mt-5 moe-min-w-[824px]"
        onChange={(activeScheduleType) => {
          setActiveWeekType(+activeScheduleType);
        }}
      >
        <WeekSlotSchedule
          value={value[activeWeekKey]}
          isDisabled={isDisabled}
          onChange={(v) => {
            onChange?.({
              ...value,
              [activeWeekKey]: v,
            });
          }}
        />
      </EveryWeekTabs>
    </div>
  );
};
