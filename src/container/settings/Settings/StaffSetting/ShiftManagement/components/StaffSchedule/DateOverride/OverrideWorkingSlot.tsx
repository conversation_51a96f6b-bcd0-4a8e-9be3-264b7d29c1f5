import React from 'react';
import { type WorkingSlotValue } from '../../../../../../../../store/staff/staff.boxes';
import { Checkbox } from '@moego/ui';
import {
  WorkingSlotDailySettingTable,
  type WorkingSlotDailySettingValue,
} from '../WorkingSlot/WorkingSlotDailySettingTable';
import { WorkingSlotListTable, type WorkingSlotListValue } from '../WorkingSlot/WorkingSlotListTable';
import { OneDayMinutes } from '../../../../../../../../store/autoMessage/autoReply.boxes';
import { getDefaultLimit } from '../../Limitation/Limitation.type';

export interface OverrideWorkingSlotProps {
  className?: string;
  value?: WorkingSlotValue;
  emptyText?: string;
  // 如果取消勾选，返回 null 值
  onChange?: (v: WorkingSlotValue) => void;
  onAdd?: (v: WorkingSlotValue) => void;
}

const defaultOverrideSlotValue: WorkingSlotValue = {
  isAvailable: false,
  slotDailySetting: {
    startTime: 0,
    endTime: OneDayMinutes - 5,
    capacity: 1,
    limit: getDefaultLimit(),
  },
  slotHourSettingList: [
    {
      startTime: 0,
      capacity: 1,
      limit: getDefaultLimit(),
    },
  ],
};

export function OverrideWorkingSlot(props: OverrideWorkingSlotProps) {
  const { className, value = defaultOverrideSlotValue, onChange } = props;

  const isNoSetting = value.slotDailySetting.startTime === -1;
  const { slotHourSettingList, slotDailySetting } = isNoSetting ? defaultOverrideSlotValue : value;

  const hasSlotList = slotHourSettingList && slotHourSettingList.length > 0;
  const slotListValue = hasSlotList ? slotHourSettingList : defaultOverrideSlotValue.slotHourSettingList;
  const isCheckboxSelected = value.isAvailable;

  const handleDailySettingChange = (v: WorkingSlotDailySettingValue) => {
    onChange?.({
      ...value,
      slotDailySetting: v,
    });
  };

  const handleWorkingSlotChange = (v: WorkingSlotListValue) => {
    onChange?.({
      ...value,
      slotHourSettingList: v,
    });
  };

  const handleCheckboxChange = (checked: boolean) => {
    onChange?.({
      ...value,
      isAvailable: checked,
    });
  };

  return (
    <div className={className}>
      <Checkbox isSelected={isCheckboxSelected} onChange={handleCheckboxChange}>
        Working on selected date(s)
      </Checkbox>
      {isCheckboxSelected && (
        <div className="moe-mt-m moe-flex moe-flex-col moe-gap-m">
          <WorkingSlotDailySettingTable size="small" value={slotDailySetting} onChange={handleDailySettingChange} />
          <WorkingSlotListTable
            size="small"
            classNames={{
              action: 'moe-gap-0',
            }}
            value={slotListValue}
            onChange={handleWorkingSlotChange}
            validTimeRange={[slotDailySetting.startTime, slotDailySetting.endTime]}
          />
        </div>
      )}
    </div>
  );
}
