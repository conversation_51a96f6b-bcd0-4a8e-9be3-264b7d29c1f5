import { Spin } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { useStore } from 'amos';
import React, { useEffect } from 'react';
import { useAsync } from 'react-use';
import { getLocationStaffList } from '../../../../../../../store/staff/staff.actions';
import { selectBusinessStaffs } from '../../../../../../../store/staff/staff.selectors';
import {
  batchCopyStaffScheduleWorkingHour,
  batchCopyStaffScheduleWorkingSlot,
  getStaffWorkingHour,
  getStaffWorkingSlot,
  setEditingWorkingHourStaffId,
} from '../../../../../../../store/staffSchedule/staffSchedule.actions';
import { AvailabilityType } from '../../../../../../../store/staffSchedule/staffSchedule.types';
import type { EnumValues } from '../../../../../../../store/utils/createEnum';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { StaffList } from '../StaffList';
import { StaffScheduleBySlot } from '../StaffSchedule/StaffScheduleBySlot';
import { StaffScheduleByTime } from '../StaffSchedule/StaffScheduleByTime';
import { DateOverride } from './DateOverride/DateOverride';
import { toastApi } from '../../../../../../../components/Toast/Toast';
import { editingWorkingHourStaffIdBox } from '../../../../../../../store/staffSchedule/staffSchedule.boxes';
import { useAvailabilityTypeFirstSwitch } from '../../hooks/useAvailabilityType';

export const StaffScheduleListView = (props: {
  businessId: number;
  availabilityType: {
    type: 'init' | 'changed';
    value: EnumValues<typeof AvailabilityType>;
  };
}) => {
  const { businessId, availabilityType } = props;
  const dispatch = useDispatch();
  const store = useStore();
  const [staffId] = useSelector(editingWorkingHourStaffIdBox);

  // 确保当前 staffId 是有效的，若传入的 staffId 无效，则默认取 staffList 的第一个 id
  // 主要场景：页面刷新时，默认取第一个 staffId
  const ensureStaffId = (staffId?: number) => {
    const staffIdList = store.select(selectBusinessStaffs(businessId));
    const firstStaffId = staffIdList.get(0);
    const defaultStaffId = isNormal(firstStaffId) ? firstStaffId : -1;
    const isStaffIdValid = isNormal(staffId) && staffIdList.includes(staffId);

    const id = isStaffIdValid ? staffId : defaultStaffId;

    if (isNormal(id) && !isStaffIdValid) {
      dispatch(setEditingWorkingHourStaffId(id));
    }
  };

  const handleStaffSettingCopy = async (sourceStaffId: number, targetStaffIdList: number[]) => {
    if (availabilityType.value === AvailabilityType.BY_TIME) {
      await dispatch(batchCopyStaffScheduleWorkingHour(sourceStaffId, targetStaffIdList));
    } else {
      await dispatch(batchCopyStaffScheduleWorkingSlot(sourceStaffId, targetStaffIdList));
    }

    toastApi.success('Copy staff setting successfully');
  };

  const { loading } = useAvailabilityTypeFirstSwitch(
    availabilityType,
    async () => {
      if (availabilityType.value === AvailabilityType.BY_TIME) {
        await dispatch(getStaffWorkingHour());
      } else {
        await dispatch(getStaffWorkingSlot());
      }
    },
    [businessId],
  );

  const { loading: initLoading } = useAsync(async () => {
    await dispatch(getLocationStaffList([`${businessId}`]));
  }, [businessId]);

  useEffect(() => {
    ensureStaffId(staffId);
  }, [staffId]);

  return (
    <Spin
      classNames={{ base: 'moe-w-full moe-h-full', container: 'moe-w-full moe-h-full' }}
      isLoading={loading || initLoading}
    >
      <div className="moe-grid moe-grid-cols-12 moe-gap-x-[20px]">
        <div className="moe-min-w-[220px] moe-max-w-[260px] moe-mr-xl moe-col-span-3 moe-sticky moe-top-0 moe-align-top">
          <StaffList
            currentId={staffId}
            onSelect={(nextStaffId) => {
              dispatch(setEditingWorkingHourStaffId(nextStaffId));
            }}
            onCopyStaffSetting={handleStaffSettingCopy}
          />
        </div>

        <div className="moe-col-span-9">
          <div>
            {availabilityType.value === AvailabilityType.BY_TIME ? (
              <StaffScheduleByTime staffId={staffId} />
            ) : (
              <StaffScheduleBySlot staffId={staffId} />
            )}
          </div>

          <DateOverride
            className="moe-mt-xl"
            staffId={staffId}
            businessId={businessId}
            availabilityType={availabilityType.value}
          />
        </div>
      </div>
    </Spin>
  );
};
