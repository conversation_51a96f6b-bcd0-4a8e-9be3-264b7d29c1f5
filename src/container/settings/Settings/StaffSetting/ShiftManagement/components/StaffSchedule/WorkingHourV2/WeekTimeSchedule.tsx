import classNames from 'classnames';
import React from 'react';
import { dayOfWeeks } from '../../../../../../../../store/business/business.boxes';
import {
  type FullWeekDay,
  FullWeekDayList,
} from '../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { defaultScheduleWorkingHourValue, type ScheduleWorkingHourValue, WorkingHourItem } from './WeekTimeItem';
import { useLatestCallback } from '../../../../../../../../utils/hooks/useLatestCallback';
import type {
  WeekServiceAreaValue,
  WeekTimeValue,
} from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import { useModal } from '../../../../../../../../components/Modal/useModal';
import { CopyPasteModal } from '../../../../../../../../components/WeekTimeScheduleV2/CopyPasteModal';
import { cloneDeep, isNil } from 'lodash';

export interface ExtraCopyProps {
  days: FullWeekDay[];
  day: number;
}

export interface WeekTimeScheduleProps {
  className?: string;
  value: WeekTimeValue;
  showServiceArea: boolean;
  areaValue?: WeekServiceAreaValue;
  isDisabled?: boolean;
  onChange?: (val: WeekTimeValue) => void;
  onAreaChange?: (v: WeekServiceAreaValue) => void;
  onCheckChange?: (v: { day: number; checked: boolean }) => void;
  onCopyClick?: (v: FullWeekDay) => void;
  children?: (day: number) => React.ReactNode;
}

export function WeekTimeSchedule(props: WeekTimeScheduleProps) {
  const { className, value, areaValue, onChange, onAreaChange, isDisabled, showServiceArea } = props;
  const openCopyModal = useModal(CopyPasteModal);

  const handleItemChange = useLatestCallback((v: ScheduleWorkingHourValue, day: Lowercase<FullWeekDay>) => {
    const { areaId, ...workingHourDayValue } = v;
    onChange?.({
      ...value,
      [day]: workingHourDayValue,
    });

    if (areaValue && onAreaChange && areaId !== undefined) {
      onAreaChange({
        ...areaValue,
        [day]: [{ areaId }],
      });
    }
  });

  const handleDailySettingCopy = useLatestCallback((day: FullWeekDay) => {
    openCopyModal({
      visible: true,
      day,
      onApply: (targetDayList) => {
        const dayKey = day.toLowerCase() as Lowercase<FullWeekDay>;
        const sourceDayData = value[dayKey];

        const newWeekData = targetDayList.reduce((weekData, targetDay) => {
          const targetDayKey = targetDay.toLowerCase() as Lowercase<FullWeekDay>;
          weekData[targetDayKey] = cloneDeep(sourceDayData);

          return weekData;
        }, {} as WeekTimeValue);

        onChange?.({
          ...value,
          ...newWeekData,
        });

        if (showServiceArea && areaValue) {
          const sourceAreaData = areaValue[dayKey];
          const newAreaWeekData = targetDayList.reduce((areaData, targetDay) => {
            const targetDayKey = targetDay.toLowerCase() as Lowercase<FullWeekDay>;
            const [{ areaId }] = sourceAreaData;
            areaData[targetDayKey] = [{ areaId }];

            return areaData;
          }, {} as WeekServiceAreaValue);

          onAreaChange?.({
            ...areaValue,
            ...newAreaWeekData,
          });
        }
      },
    });
  });

  return (
    <div className={classNames('moe-flex moe-flex-col', className)}>
      {dayOfWeeks.sundayFirst.map((dayIndex) => {
        const day: FullWeekDay = FullWeekDayList[dayIndex];
        const lowercaseDayKey = day.toLowerCase() as Lowercase<FullWeekDay>;
        const dayData = value[lowercaseDayKey] ?? defaultScheduleWorkingHourValue;
        const areaId = areaValue?.[lowercaseDayKey]?.[0]?.areaId;

        const mergedWorkingHourValue = !isNil(areaId)
          ? {
              ...dayData,
              areaId,
            }
          : dayData;

        return (
          <WorkingHourItem
            showServiceArea={showServiceArea}
            isDisabled={isDisabled}
            className="moe-py-s"
            key={day}
            day={day}
            value={mergedWorkingHourValue}
            onChange={handleItemChange}
            onCopyClick={handleDailySettingCopy}
          />
        );
      })}
    </div>
  );
}
