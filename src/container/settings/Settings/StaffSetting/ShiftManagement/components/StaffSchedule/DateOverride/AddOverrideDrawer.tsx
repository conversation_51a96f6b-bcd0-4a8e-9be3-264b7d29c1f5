import { Drawer as <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { sleep } from 'monofile-utilities/lib/sleep';
import React, { useEffect, useRef, useState } from 'react';
import IconCrown from '../../../../../../../../assets/svg/pricing-icon-crown.svg';
import { Button } from '../../../../../../../../components/Button/Button';
import { SvgIcon } from '../../../../../../../../components/Icon/Icon';
import { useUpgradeGuideModal } from '../../../../../../../../components/UpgradeGuideModal/useUpgradeGuideModal';
import { selectBusiness } from '../../../../../../../../store/business/business.selectors';
import { getClosedDateList } from '../../../../../../../../store/business/closedDate.actions';
import { getHolidays } from '../../../../../../../../store/business/holiday.actions';
import { FullWeekDayList } from '../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import {
  addStaffDateOverride,
  getStaffDateOverride,
  modifyStaffDateOverride,
} from '../../../../../../../../store/staffSchedule/staffSchedule.actions';
import {
  AvailabilityType,
  type DateOverrideDrawerValue,
} from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import { useLatestCallback } from '../../../../../../../../utils/hooks/useLatestCallback';
import { useToggleIntercom } from '../../../../../../../../utils/hooks/useToggleIntercom';
import { OverrideDrawerContentView, OverrideDrawerWrapView } from '../../../StaffSchedule.style';
import { useShiftManagementAuth } from '../../UpgradeIntercept';
import { EditingStaffProfile } from './EditingStaffProfile';
import { OverrideWorkingTime } from './OverrideWorkingTime';
import { SimpleCalendar } from './SimpleCalendar';
import { selectStaffAllOverrideDates } from '../../../../../../../../store/staffSchedule/staffSchedule.selectors';
import { useRotatingWeekFallbackBiz } from '../../../hooks/useCalcRotatingWeek';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../../../utils/DateTimeUtil';
import { getDefaultLimit } from '../../Limitation/Limitation.type';
import { useSerialCallback } from '@moego/tools';

export interface AddOverrideDrawerProps {
  staffId: number;
  visible?: boolean;
  onClose?: () => void;
  defaultDate?: string;
  value?: DateOverrideDrawerValue;
  onChange?: (_: DateOverrideDrawerValue) => void;
  businessId: number;
}

export const defaultOverrideDrawerValue: DateOverrideDrawerValue = {
  dates: [],
  value: {
    isAvailable: false,
    timeRange: [],
    limit: getDefaultLimit(),
  },
  workingArea: [],
};

export function AddOverrideDrawer(props: AddOverrideDrawerProps) {
  const { visible, onClose, staffId, defaultDate, businessId, value } = props;
  const ref = useRef<HTMLDivElement>(null);
  const hasAuth = useShiftManagementAuth();
  const dispatch = useDispatch();
  const [business, overrides] = useSelector(selectBusiness(businessId), selectStaffAllOverrideDates(staffId));

  const [innerValue, setInnerValue] = useState<DateOverrideDrawerValue>(value || defaultOverrideDrawerValue);
  const { dates = [], value: overrideValue, workingArea } = innerValue;

  const isEdit = !!defaultDate; // 如果有传入defaultDate，说明是从某一天的日历点击进来的，此时我们会收起日历，让时间段列表和service area显示在上面
  const hasSelectedDate = isEdit || dates.length > 0;
  const { renderUpgradeGuideModal, openUpgradeGuideModal } = useUpgradeGuideModal({
    permissionKey: 'rotatingSchedule',
  });
  const getDefaultTimeRange = useRotatingWeekFallbackBiz(staffId, AvailabilityType.BY_TIME);

  useToggleIntercom(visible);

  const renderDate = useLatestCallback((d: dayjs.Dayjs | string) => {
    return d ? `${business.formatDate(d)} ${FullWeekDayList[dayjs(d).get('day')]}` : '';
  });

  const onConfirm = useSerialCallback(async () => {
    if (isEdit) {
      await dispatch(modifyStaffDateOverride(defaultDate, staffId, innerValue));
    } else {
      await dispatch(addStaffDateOverride(staffId, innerValue));
    }

    await dispatch(getStaffDateOverride([staffId]));
    onClose?.();
  });

  useEffect(() => {
    if (visible) {
      dispatch([getHolidays(businessId), getClosedDateList(businessId)]);
    } else {
      setInnerValue(defaultOverrideDrawerValue);
    }
  }, [visible]);

  useEffect(() => {
    setInnerValue(value || defaultOverrideDrawerValue);
  }, [value]);

  const renderNoAuthTip = () =>
    !hasAuth && (
      <>
        <div className="!moe-flex !moe-items-start !moe-p-[16px] !moe-rounded-[8px] !moe-border-solid !moe-border-[1px] !moe-border-[#E6E6E6] !moe-text-[#333]">
          <SvgIcon src={IconCrown} color="#FAAD14" size={14} className="!moe-mt-[3px]" />
          <div className="!moe-ml-[10px]">
            <p className="!moe-mb-[8px]">
              Unlock hassle-free scheduling experience with new shift management solution!
            </p>
            <Button
              btnType="secondary"
              buttonRadius="circle"
              className="!moe-py-[6px] !moe-px-[12px] !moe-font-bold !moe-text-[12px] !moe-leading-[20px] !moe-bg-brand-bold hover:!moe-bg-brand-hover hover:!moe-border-brand-hover !moe-shadow-none"
              onClick={() => {
                openUpgradeGuideModal();
              }}
            >
              Upgrade
            </Button>
          </div>
        </div>
        {renderUpgradeGuideModal({ style: { zIndex: 1050 } })}
      </>
    );

  return (
    <MoeDrawer
      isDismissable={false}
      title={isEdit ? 'Edit date override' : 'Add date override'}
      isOpen={visible}
      onClose={onClose}
      confirmButtonProps={{
        isLoading: onConfirm.isBusy(),
        isDisabled: !hasSelectedDate || !hasAuth,
      }}
      confirmText="Save"
      onConfirm={onConfirm}
      className="moe-w-[600px]"
    >
      <Text variant="small">Date override updates the working hours and online booking availability.</Text>
      <OverrideDrawerWrapView className="!moe-h-full !moe-flex !moe-flex-col">
        <div className="!moe-flex !moe-flex-col !moe-flex-1 !moe-pb-[0]" ref={ref}>
          {hasAuth && <EditingStaffProfile staffId={staffId} className="moe-mt-m" />}
          {renderNoAuthTip()}
          <OverrideDrawerContentView className={classNames('moe-flex moe-flex-col', { disabled: !hasAuth })}>
            <SimpleCalendar
              staffId={staffId}
              isEdit={isEdit}
              defaultDate={dayjs(defaultDate)}
              value={dates}
              dateFormatMD={(date) => date.format(business.dateFormatMD)}
              onChange={(nextDates) => {
                if (isEdit && nextDates.length === 0) {
                  return;
                }

                const first = nextDates?.[0];
                const params: DateOverrideDrawerValue = { ...innerValue, dates: nextDates };
                if (dates.length === 0 && !overrideValue.isAvailable && first) {
                  const fmted = first.format(DATE_FORMAT_EXCHANGE);
                  const isExistingOverride = overrides.find((i) => i.overrideDate === fmted);
                  const times = isExistingOverride
                    ? isExistingOverride.value.timeRange
                    : getDefaultTimeRange(first).currentTimeRange;
                  params.value.timeRange = [...times];
                } else if (nextDates.length === 0) {
                  params.value.isAvailable = false;
                }
                setInnerValue(params);
              }}
              businessId={businessId}
            />
            {hasSelectedDate && (
              <OverrideWorkingTime
                value={{
                  ...overrideValue,
                  workingArea,
                }}
                businessId={businessId}
                onChange={({ workingArea, ...value }) =>
                  setInnerValue((pre) => ({
                    ...pre,
                    value,
                    workingArea,
                  }))
                }
                onAdd={async () => {
                  // after sync with dom, then scroll
                  await sleep(0);
                  if (ref.current) {
                    ref.current.scrollTo({
                      top: ref.current.scrollHeight,
                      behavior: 'smooth',
                    });
                  }
                }}
                emptyText={dates.length > 1 ? undefined : `Not working on ${renderDate(dates[0])}`}
              />
            )}
          </OverrideDrawerContentView>
        </div>
      </OverrideDrawerWrapView>
    </MoeDrawer>
  );
}
