import { MinorMoreOutlined } from '@moego/icons-react';
import { Dropdown, IconButton, Text, Typography } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import dayjs from 'dayjs';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import React from 'react';
import IconCalendarCheck from '../../../../../../../../assets/svg/calendar-schedule-check.svg';
import { SvgIcon } from '../../../../../../../../components/Icon/Icon';
import { toastApi } from '../../../../../../../../components/Toast/Toast';
import { selectBusiness } from '../../../../../../../../store/business/business.selectors';
import { deleteStaffDateOverride } from '../../../../../../../../store/staffSchedule/staffSchedule.actions';
import { editingWorkingHourStaffIdBox } from '../../../../../../../../store/staffSchedule/staffSchedule.boxes';
import { AvailabilityType, DateOverrideType } from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../../../utils/DateTimeUtil';
import { useGetServiceAreaInfoById, useGetServiceAreaRangeByDate } from '../../../hooks/useServiceArea';
import SvgIconAnimalPrintPinSvg from '../../../../../../../../assets/svg/icon-animal-print-pin.svg';
import { DEFAULT_SERVICE_AREA_CONFIG } from '../../../../../../../../components/ServiceArea/ServiceArea.options';
import { useStaffScheduleDrawerContext } from '../StaffScheduleDrawerContext';
import { useSerialCallback } from '@moego/tools';
import { useWorkingHourByDate } from '../../../hooks/useWorkingHourByDate';

export interface OverrideItemProps {
  type: DateOverrideType;
  date: string;
  businessId: number;
}

export function OverrideItem(props: OverrideItemProps) {
  const { date, type, businessId } = props;
  const editable = type === DateOverrideType.Ongoing;
  const dispatch = useDispatch();
  const [business, staffId] = useSelector(selectBusiness(businessId), editingWorkingHourStaffIdBox);
  const { openAddOverrideDrawer } = useStaffScheduleDrawerContext();

  const getServiceAreaInfoById = useGetServiceAreaInfoById(businessId);
  const workingArea = useGetServiceAreaRangeByDate(staffId, date, type);
  const serviceInfo = workingArea && getServiceAreaInfoById(workingArea?.[0]?.areaId);

  const workingHour = useWorkingHourByDate(staffId, date, type);
  const isWorking = workingHour.isAvailable;

  const onDelete = useSerialCallback(async () => {
    await dispatch(deleteStaffDateOverride(date, staffId, AvailabilityType.BY_TIME));
    toastApi.success('Date override has been deleted successfully.');
  });

  return (
    <div className="hover:!moe-bg-neutral-sunken-0 !moe-cursor-pointer !moe-flex !moe-items-center !moe-justify-between !moe-px-[16px] !moe-py-[20px] !moe-border-0 !moe-border-b !moe-border-solid !moe-border-b-[#E6E6E6] last:!moe-border-b-0">
      <div className="moe-flex moe-flex-1 moe-gap-x-[40px]">
        <div className="moe-w-[150px] moe-whitespace-nowrap">
          <SvgIcon src={IconCalendarCheck} size={20} color="#3D414B" className="!moe-mr-[8px] !moe-align-top" />
          <Typography.Heading size="5" className="moe-inline-block">
            {dayjs(date).format('ddd')}, {business.formatDate(date)}
          </Typography.Heading>
        </div>
        <div className="moe-w-[160px] moe-flex moe-flex-col moe-gap-y-[4px]">
          {isWorking ? (
            workingHour.timeRange.map((i, index) => {
              return (
                <Text variant="regular-short" className="moe-text-secondary" key={index}>
                  {business.formatFixedTime(i.startTime * T_MINUTE)} – {business.formatFixedTime(i.endTime * T_MINUTE)}
                </Text>
              );
            })
          ) : (
            <Text variant="regular-short" className="moe-text-secondary">
              Not working
            </Text>
          )}
        </div>
        {workingArea && isWorking && (
          <div className="moe-flex moe-flex-1 moe-items-center moe-px-[16px]">
            <SvgIcon
              src={SvgIconAnimalPrintPinSvg}
              size={20}
              color={serviceInfo?.colorCode || DEFAULT_SERVICE_AREA_CONFIG.color}
            ></SvgIcon>
            <Text variant="regular-short" className="moe-ml-xs">
              {serviceInfo?.areaName || DEFAULT_SERVICE_AREA_CONFIG.label}
            </Text>
          </div>
        )}
      </div>
      <div>
        {editable && (
          <Dropdown>
            <Dropdown.Trigger>
              <IconButton icon={<MinorMoreOutlined />} color="transparent" />
            </Dropdown.Trigger>
            <Dropdown.Menu
              onAction={(k) => {
                if (k === 'edit') {
                  openAddOverrideDrawer({
                    staffId,
                    businessId,
                    defaultDate: date,
                    value: {
                      dates: [dayjs(date, DATE_FORMAT_EXCHANGE)],
                      value: workingHour,
                      workingArea,
                    },
                  });
                }
                if (k === 'delete') {
                  if (onDelete.isBusy()) {
                    return;
                  }
                  onDelete();
                }
              }}
            >
              <Dropdown.Item key="edit">Edit</Dropdown.Item>
              <Dropdown.Item key="delete">Delete</Dropdown.Item>
            </Dropdown.Menu>
          </Dropdown>
        )}
      </div>
    </div>
  );
}
