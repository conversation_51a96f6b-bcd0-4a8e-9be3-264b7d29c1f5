import { Heading, Spin } from '@moego/ui';
import React, { useState } from 'react';
import { AvailabilityType, DateOverrideType } from '../../../../../../../../store/staffSchedule/staffSchedule.types';
import type { EnumValues } from '../../../../../../../../store/utils/createEnum';
import { AddOverride } from './AddOverride';
import { OverrideSwitcher } from './OverrideSwitcher';
import { OverridesBySlot } from './OverridesBySlot';
import { OverridesByTime } from './OverridesByTime';
import { useDispatch } from 'amos';
import {
  getStaffDateOverride,
  getStaffSlotOverride,
} from '../../../../../../../../store/staffSchedule/staffSchedule.actions';
import { isNormal } from '../../../../../../../../store/utils/identifier';
import { useAsync } from 'react-use';
import { SMTestIds } from '../../../../../../../../config/testIds/shiftManagement';

export interface DateOverrideProps {
  className?: string;
  staffId: number;
  businessId: number;
  availabilityType: EnumValues<typeof AvailabilityType>;
}

export function DateOverride(props: DateOverrideProps) {
  const { businessId, availabilityType, staffId, className } = props;
  const dispatch = useDispatch();

  const [type, setType] = useState<DateOverrideType>(DateOverrideType.Ongoing);

  const getOverrideDataAsync = async (staffId: number) => {
    if (!isNormal(staffId)) return;

    if (availabilityType === AvailabilityType.BY_TIME) {
      await dispatch(getStaffDateOverride([staffId]));
    } else {
      await dispatch(getStaffSlotOverride([staffId]));
    }
  };

  const { loading } = useAsync(async () => {
    await getOverrideDataAsync(staffId);
  }, [staffId, availabilityType]);

  return (
    <Spin isLoading={loading}>
      <div className={className}>
        <div className="moe-flex moe-items-center moe-justify-between">
          <div className="moe-flex moe-items-center moe-gap-[12px]">
            <Heading size="5">Date override:</Heading>
            <OverrideSwitcher value={type} onChange={setType} />
          </div>
          <div>
            <AddOverride className="-moe-mr-m" businessId={businessId} availabilityType={availabilityType} />
          </div>
        </div>

        <div className="moe-pb-m moe-pt-[6px]" data-testid={SMTestIds.SettingDateOverrideList}>
          {availabilityType === AvailabilityType.BY_TIME ? (
            <OverridesByTime businessId={businessId} type={type} />
          ) : (
            <OverridesBySlot businessId={businessId} type={type} />
          )}
        </div>
      </div>
    </Spin>
  );
}
