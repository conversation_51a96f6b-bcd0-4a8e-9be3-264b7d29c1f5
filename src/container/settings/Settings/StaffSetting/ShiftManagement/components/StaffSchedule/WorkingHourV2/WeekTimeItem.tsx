import React, { memo } from 'react';
import cn from 'classnames';
import { MinorCopyOutlined } from '@moego/icons-react';
import { Checkbox, IconButton, Markup, Text } from '@moego/ui';
import { TimePeriods } from '../../../../../../../../components/WeekTimeScheduleShiftManagement/TimePeriods';
import type { LimitationFormValue } from '../../Limitation/LimitationFormContext';
import type { TimePeriod } from '../../../../../../../../components/WeekTimeScheduleShiftManagement/types';
import type { FullWeekDay } from '../../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import type { WorkingHourValue } from '../../../../../../../../store/staff/staff.boxes';
import { LimitationActions } from '../../Limitation/LimitationActions';
import { getDefaultLimit } from '../../Limitation/Limitation.type';
import { ServiceAreaSelector } from '../../ServiceArea';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';
import { StaffScheduleTestIds } from '../../../../../../../../config/testIds/staffSchedule';

export type ScheduleWorkingHourValue = WorkingHourValue & {
  // 只有 mobile grooming 才有这个字段，为空则不渲染
  areaId?: number;
};

export interface WorkingHourItemProps {
  className?: string;
  showServiceArea: boolean;
  value: ScheduleWorkingHourValue;
  onChange?: (v: ScheduleWorkingHourValue, day: Lowercase<FullWeekDay>) => void;
  onCopyClick?: (day: FullWeekDay) => void;
  day: FullWeekDay;
  isDisabled?: boolean;
  /** 最大时间，用于限制 time range 的最大值 */
  maxTime?: number;
}

export const defaultScheduleWorkingHourValue = {
  isAvailable: true,
  timeRange: [
    {
      startTime: 540,
      endTime: 1020,
    },
  ],
  limit: getDefaultLimit(),
};

export const WorkingHourItem = memo((props: WorkingHourItemProps) => {
  const [business] = useSelector(selectCurrentBusiness());
  const { value, onChange, onCopyClick, day, maxTime, className, isDisabled, showServiceArea } = props;
  const { timeRange, limit, isAvailable, areaId } = value;
  const isWorkingAvailable = isAvailable && timeRange.length > 0;
  const dayKey = day.toLowerCase() as Lowercase<FullWeekDay>;

  const handleCheckChange = (isSelected: boolean) => {
    let nextTimeRange = value.timeRange;

    if (isSelected && nextTimeRange.length === 0) {
      nextTimeRange = defaultScheduleWorkingHourValue.timeRange;
    }

    onChange?.(
      {
        ...value,
        timeRange: nextTimeRange,
        isAvailable: isSelected,
      },
      dayKey,
    );
  };

  const handleTimeRangeChange = (nextTimeRange: TimePeriod[]) => {
    onChange?.(
      {
        ...value,
        timeRange: nextTimeRange.map(({ startTime, endTime }) => ({ startTime, endTime })),
      },
      day.toLowerCase() as Lowercase<FullWeekDay>,
    );
  };

  const handleAreaChange = (areaId: number) => {
    onChange?.(
      {
        ...value,
        areaId,
      },
      day.toLowerCase() as Lowercase<FullWeekDay>,
    );
  };

  const handleLimitChange = (limit: LimitationFormValue) => {
    onChange?.(
      {
        ...value,
        limit,
      },
      dayKey,
    );
  };

  const handleCopyClick = () => {
    onCopyClick?.(day);
  };

  return (
    <div className={className} data-testid={`${StaffScheduleTestIds.WorkingHourScheduleTime}-${day}`}>
      <div className="moe-flex moe-items-center">
        <Checkbox
          classNames={{
            wrapper: 'moe-items-center',
          }}
          isDisabled={isDisabled}
          isSelected={isWorkingAvailable}
          onChange={handleCheckChange}
        >
          <Markup variant="regular" className={cn({ 'moe-font-normal': !isAvailable })}>
            {day}
          </Markup>
        </Checkbox>

        {isWorkingAvailable && (
          <IconButton
            className="moe-text-tertiary moe-ml-[6px] moe-h-[24px]"
            style={{
              backgroundColor: 'transparent',
            }}
            isDisabled={isDisabled}
            icon={<MinorCopyOutlined />}
            onPress={handleCopyClick}
            color="transparent"
          />
        )}
        {!isWorkingAvailable && (
          <Text variant="regular" className="moe-ml-m moe-text-tertiary">
            Not working on {day}
          </Text>
        )}
      </div>

      {isWorkingAvailable && (
        <div className="moe-ml-[28px] moe-mt-[12px]">
          <div className="moe-flex moe-items-start moe-gap-xs">
            <Markup variant="small" className="moe-text-secondary moe-w-[120px] moe-leading-[40px]">
              Working time:
            </Markup>
            <TimePeriods
              className="moe-relative moe-w-[280px]"
              isDisabled={isDisabled}
              maxTime={maxTime}
              value={timeRange}
              onChange={handleTimeRangeChange}
              addable={(v, i) => {
                const isLast = i === v.length - 1;

                return !isDisabled && isLast;
              }}
              deletable={() => !isDisabled}
            />
          </div>

          {/* just for SM staff schedule mobile grooming */}
          {showServiceArea && (
            <div className="moe-mt-[12px] moe-flex moe-items-start moe-gap-xs">
              <Markup variant="small" className="moe-text-secondary moe-w-[120px] moe-leading-[32px]">
                Service area:
              </Markup>
              <ServiceAreaSelector
                className="moe-w-[280px]"
                businessId={business.id}
                value={areaId}
                onChange={handleAreaChange}
              />
            </div>
          )}

          <div className="moe-mt-[12px] moe-flex moe-items-start moe-gap-xs">
            <Markup variant="small" className="moe-text-secondary moe-w-[120px] moe-leading-[32px]">
              Booking limit:
            </Markup>
            <LimitationActions limit={limit} onChange={handleLimitChange} isDisabled={isDisabled} />
          </div>
        </div>
      )}
    </div>
  );
});
