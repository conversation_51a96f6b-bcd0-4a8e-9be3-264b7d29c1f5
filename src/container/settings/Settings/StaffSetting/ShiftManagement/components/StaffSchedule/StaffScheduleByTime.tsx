import React from 'react';
import { useSelector, useDispatch } from 'amos';
import { selectStaffScheduleByTime } from '../../../../../../../store/staffSchedule/staffSchedule.selectors';
import { useBizWorkingHour } from '../../hooks/useBizOpeningHour';
import { staffMapBox } from '../../../../../../../store/staff/staff.boxes';
import {
  updateStaffScheduleWorkingHour,
  updateStaffServiceArea,
} from '../../../../../../../store/staffSchedule/staffSchedule.actions';
import { StaffScheduleByTimeView } from './StaffScheduleByTimeView';
import { staffScheduleServiceAreaMapBox } from '../../../../../../../store/staffSchedule/staffSchedule.boxes';
import { selectCurrentBusiness } from '../../../../../../../store/business/business.selectors';

interface StaffScheduleByTimeProps {
  staffId: number;
}

export const StaffScheduleByTime = (props: StaffScheduleByTimeProps) => {
  const { staffId } = props;

  const dispatch = useDispatch();
  const [staffSchedule, serviceArea, business, staff] = useSelector(
    selectStaffScheduleByTime(staffId),
    staffScheduleServiceAreaMapBox.mustGetItem(staffId),
    selectCurrentBusiness(),
    staffMapBox.mustGetItem(staffId),
  );

  const showServiceArea = business.isMobileGrooming();

  useBizWorkingHour();

  if (!staffSchedule.isValidStaff) return null;

  return (
    <StaffScheduleByTimeView
      showServiceArea={showServiceArea}
      staff={staff}
      value={staffSchedule}
      areaValue={serviceArea}
      scheduleType={staffSchedule.scheduleType}
      dateScope={[staffSchedule.startDate, staffSchedule.endDate]}
      onScheduleTypeChange={(v) => {
        dispatch(
          updateStaffScheduleWorkingHour(staffId, {
            scheduleType: v,
          }),
        );
      }}
      onChange={(value) => {
        dispatch(updateStaffScheduleWorkingHour(staffId, value));
      }}
      onAreaChange={(value) => {
        dispatch(updateStaffServiceArea(staffId, value));
      }}
    />
  );
};

StaffScheduleByTime.displayName = 'StaffScheduleByTime';
