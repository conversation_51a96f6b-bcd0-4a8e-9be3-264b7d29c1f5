import React, { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import { Heading } from '@moego/ui';
import { ScheduleParams, type ScheduleParamsValue } from './ScheduleParams';
import {
  type RotateWeekKeyType,
  type WeekTimeValue,
  type WeekServiceAreaValue,
  MaxStaffEndDate,
  RotateWeekList,
  WeekKeyMapNum,
} from '../../../../../../../store/staffSchedule/staffSchedule.types';
import { EveryWeekTabs } from '../EveryWeekTabs';
import { WeekTimeSchedule } from './WorkingHourV2/WeekTimeSchedule';
import type { StaffRecord } from '../../../../../../../store/staff/staff.boxes';
import type { Dayjs } from 'dayjs';

export type StaffScheduleByTimeViewValue = {
  [K in RotateWeekKeyType]: WeekTimeValue;
};

export type StaffScheduleByTimeViewAreaValue = {
  [K in RotateWeekKeyType]: WeekServiceAreaValue;
};

interface StaffScheduleByTimeViewProps {
  className?: string;
  showServiceArea: boolean;
  value: StaffScheduleByTimeViewValue;
  areaValue?: StaffScheduleByTimeViewAreaValue;
  staff: StaffRecord;
  // rotating week 的日期计算范围，默认是 today 到 MaxStaffEndDate
  dateScope: [Dayjs, Dayjs];
  scheduleType: WeekKeyMapNum;
  onScheduleTypeChange: (scheduleType: WeekKeyMapNum) => void;
  onChange?: (value: StaffScheduleByTimeViewValue) => void;
  onAreaChange?: (value: StaffScheduleByTimeViewAreaValue) => void;
  isDisabled?: boolean;
}

const defaultDateScope: [Dayjs, Dayjs] = [dayjs().startOf('week'), MaxStaffEndDate];

/** 用于复用的纯渲染组件，只根据数据进行渲染，不要使用 dispatch、select 等 */
export const StaffScheduleByTimeView = (props: StaffScheduleByTimeViewProps) => {
  const {
    showServiceArea,
    value,
    areaValue,
    staff,
    dateScope = defaultDateScope,
    scheduleType,
    onScheduleTypeChange,
    onChange,
    onAreaChange,
    className,
  } = props;
  const [activeWeekType, setActiveWeekType] = useState<WeekKeyMapNum>(WeekKeyMapNum.firstWeek);
  const activeWeekKey = RotateWeekList[activeWeekType - 1];

  const handleWeekTimeChange = (v: WeekTimeValue) => {
    onChange?.({
      ...value,
      [activeWeekKey]: v,
    });
  };

  const handleWeekAreaChange = (v: WeekServiceAreaValue) => {
    if (showServiceArea && areaValue) {
      onAreaChange?.({
        ...areaValue,
        [activeWeekKey]: v,
      });
    }
  };

  const handleScheduleTypeChange = (v: ScheduleParamsValue) => {
    setActiveWeekType((prevActiveWeekType) => {
      const nextScheduleType = v.scheduleType;
      return prevActiveWeekType <= nextScheduleType ? prevActiveWeekType : WeekKeyMapNum.firstWeek;
    });
    onScheduleTypeChange?.(v.scheduleType);
  };

  useEffect(() => {
    setActiveWeekType((prevActiveWeekType) =>
      prevActiveWeekType <= scheduleType ? prevActiveWeekType : WeekKeyMapNum.firstWeek,
    );
  }, [scheduleType]);

  return (
    <div className={className}>
      <div className="moe-w-full">
        <Heading size="5" className="moe-mb-2">
          {staff.firstName}&apos;s regular working hours
        </Heading>
        <ScheduleParams
          value={{
            scheduleType,
            startDate: dateScope[0],
            endDate: dateScope[1],
          }}
          onChange={handleScheduleTypeChange}
        />
        <EveryWeekTabs
          className="moe-border-divider moe-border moe-rounded-m moe-py-s moe-px-5 moe-mt-5 moe-min-w-[824px]"
          value={activeWeekType}
          onChange={(activeScheduleType) => {
            setActiveWeekType(activeScheduleType);
          }}
          dateScope={dateScope}
          scheduleType={scheduleType}
        >
          <WeekTimeSchedule
            showServiceArea={showServiceArea}
            value={value[activeWeekKey]}
            areaValue={areaValue?.[activeWeekKey]}
            onChange={handleWeekTimeChange}
            onAreaChange={handleWeekAreaChange}
          />
        </EveryWeekTabs>
      </div>
    </div>
  );
};
