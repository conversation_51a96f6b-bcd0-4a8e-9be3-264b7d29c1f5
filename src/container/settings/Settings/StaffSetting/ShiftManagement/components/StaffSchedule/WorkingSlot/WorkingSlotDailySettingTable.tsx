import { MinorCopyOutlined, MinorEditOutlined, MinorInfoOutlined } from '@moego/icons-react';
import {
  Heading,
  IconButton,
  Input,
  Markup,
  Text,
  TimeRangePicker,
  Tooltip,
  type TimeRangePickerProps,
} from '@moego/ui';
import React from 'react';
import SvgFormRequiredSvg from '../../../../../../../../assets/svg/form-required.svg';
import { SvgIcon } from '../../../../../../../../components/Icon/Icon';
import type { WorkingSlotValue } from '../../../../../../../../store/staff/staff.boxes';
import { LimitationRow } from '../../Limitation/LimitationRow';
import { useSelector } from 'amos';
import { selectCurrentBusiness } from '../../../../../../../../store/business/business.selectors';
import { TimeFormat } from '@moego/api-web/moego/models/organization/v1/company_enums';
import dayjs from 'dayjs';
import cn from 'classnames';
import { StaffScheduleTestIds } from '../../../../../../../../config/testIds/staffSchedule';
import { SlotDailySettingDrawer } from './SlotDailySettingDrawer';
import { useBool } from '../../../../../../../../utils/hooks/useBool';

export type WorkingSlotDailySettingValue = WorkingSlotValue['slotDailySetting'];

interface WorkingSlotDailySettingTableProps {
  className?: string;
  classNames?: {
    action?: string;
  };
  title?: string;
  value: WorkingSlotDailySettingValue;
  // small size for drawer render
  size?: 'small' | 'default';
  hideDailyWorkingHour?: boolean;
  onChange?: (v: WorkingSlotDailySettingValue) => void;
  onCopy?: () => void;
  isDisabled?: boolean;
}

export const WorkingSlotDailySettingTable = (props: WorkingSlotDailySettingTableProps) => {
  const {
    title,
    value,
    onChange,
    className,
    onCopy,
    size = 'default',
    classNames,
    isDisabled,
    hideDailyWorkingHour = false,
  } = props;
  const { startTime, endTime, capacity } = value;
  const [business] = useSelector(selectCurrentBusiness);
  const isDrawerOpen = useBool(false);

  const onDailyTimeRangeChange: TimeRangePickerProps['onChange'] = (v) => {
    onChange?.({ ...value, startTime: v?.[0]?.getMinutes() ?? 0, endTime: v?.[1]?.getMinutes() ?? 0 });
  };

  const onCapacityChange = (v: number | null) => {
    if (v === null) return;
    onChange?.({ ...value, capacity: v });
  };

  const onDrawerEditChange = (v: WorkingSlotDailySettingValue) => {
    onChange?.(v);
  };

  return (
    <div className={className}>
      {title && (
        <div className="moe-flex moe-justify-between">
          <Heading size="5" className="moe-flex moe-items-center moe-gap-xxs moe-mb-[12px]">
            <span>{title}</span>
            <Tooltip
              content="When daily settings conditions are met, no further appointments can be booked for that day"
              side="top"
            >
              <MinorInfoOutlined className="moe-cursor-pointer moe-text-tertiary" />
            </Tooltip>
          </Heading>
          {onCopy && (
            <IconButton
              isDisabled={isDisabled}
              icon={<MinorCopyOutlined />}
              onPress={onCopy}
              color="transparent"
              data-testid={StaffScheduleTestIds.SlotScheduleDailyCopyBtn}
            />
          )}
        </div>
      )}

      {/* Header Row */}
      <div className="moe-flex moe-gap-m moe-border-b moe-border-divider">
        {!hideDailyWorkingHour && (
          <div className="moe-flex moe-items-center moe-w-[260px] moe-flex-shrink-0">
            <Markup className="moe-text-secondary" variant="small">
              Working hours
            </Markup>
            <SvgIcon src={SvgFormRequiredSvg} color="#F3413B" className="!moe-ml-0 moe-mr-xxs" />
          </div>
        )}

        <Markup className="moe-text-secondary moe-flex moe-items-center moe-flex-1" variant="small">
          Daily pet maximum
        </Markup>
      </div>

      <div className="moe-border-b moe-border-divider">
        {/* Content Rows */}
        <div
          className="moe-flex moe-gap-m moe-py-[12px]"
          data-testid={StaffScheduleTestIds.SlotScheduleDailySettingRow}
        >
          {!hideDailyWorkingHour && (
            <div className="moe-w-[260px] moe-flex-shrink-0">
              <TimeRangePicker
                isDisabled={isDisabled}
                isClearable={false}
                value={[dayjs().setMinutes(startTime), dayjs().setMinutes(endTime)]}
                minuteStep={5}
                onChange={onDailyTimeRangeChange}
                use12Hours={business.timeFormatType === TimeFormat.HOUR_12}
                data-testid={StaffScheduleTestIds.SlotScheduleDailyHourTimePicker}
              />
            </div>
          )}

          <div className="moe-flex moe-justify-between moe-flex-1">
            <div className="moe-flex moe-items-center">
              <Input.Number
                className={cn('moe-w-[180px]', {
                  'moe-w-[140px]': size === 'small',
                })}
                isStepper
                isDisabled={isDisabled}
                defaultValue={1}
                step={1}
                minValue={0}
                value={capacity}
                onChange={onCapacityChange}
                data-testid={StaffScheduleTestIds.SlotScheduleDailyPetCapacityInput}
              />
              <Text className="moe-ml-xs moe-whitespace-nowrap" variant="regular-short">
                pets
              </Text>
            </div>

            <div
              className={cn('moe-flex moe-items-center moe-justify-end moe-gap-xs', classNames?.action)}
              style={{
                gap: size === 'small' ? 0 : '8px',
              }}
            >
              <IconButton
                isDisabled={isDisabled}
                icon={<MinorEditOutlined />}
                color="transparent"
                onPress={() => isDrawerOpen.open()}
                data-testid={StaffScheduleTestIds.SlotScheduleDailyEditBtn}
              />
            </div>
          </div>
        </div>

        <LimitationRow className="moe-mb-[12px]" limit={value.limit} data-testid={StaffScheduleTestIds.LimitationRow} />
      </div>
      <SlotDailySettingDrawer
        title={title}
        visible={isDrawerOpen.value}
        onClose={isDrawerOpen.close}
        value={value}
        onChange={onDrawerEditChange}
        hideDailyWorkingHour={hideDailyWorkingHour}
      />
    </div>
  );
};
