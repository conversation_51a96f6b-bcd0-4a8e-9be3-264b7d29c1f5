import { Checkbox, Heading } from '@moego/ui';
import React from 'react';
import { TimePeriods } from '../../../../../../../../components/WeekTimeScheduleShiftManagement/TimePeriods';
import { OneDayMinutes } from '../../../../../../../../store/autoMessage/autoReply.boxes';
import {
  type WorkingHourValue,
  type WorkingHourTimeRange,
  type ServiceAreaRange,
} from '../../../../../../../../store/staff/staff.boxes';
import { useControllableValue } from '../../../../../../../../utils/hooks/useControlledValue';
import { getDefaultLimit } from '../../Limitation/Limitation.type';
import { LimitationActions } from '../../Limitation/LimitationActions';
import { type LimitationFormValue } from '../../Limitation/LimitationFormContext';
import cn from 'classnames';
import { useSelector } from 'amos';
import { selectBusiness } from '../../../../../../../../store/business/business.selectors';
import { ServiceAreaSelector } from '../../ServiceArea';
import { selectPricingPermission } from '../../../../../../../../store/company/company.selectors';

type OverrideWorkingTimeValue = WorkingHourValue & {
  workingArea?: ServiceAreaRange[];
};

export interface OverrideWorkingTimeProps {
  className?: string;
  businessId: number;
  value?: OverrideWorkingTimeValue;
  defaultValue?: OverrideWorkingTimeValue;
  emptyText?: string;
  onChange?: (v: OverrideWorkingTimeValue) => void;
  onAdd?: (v: OverrideWorkingTimeValue) => void;
}

export const defaultOverrideWorkingTimeValue: OverrideWorkingTimeValue = {
  isAvailable: false,
  timeRange: [{ startTime: 0, endTime: OneDayMinutes - 5 }],
  limit: getDefaultLimit(),
  workingArea: [],
};

const MinuteStep = 5;

export function OverrideWorkingTime(props: OverrideWorkingTimeProps) {
  const { className, onAdd, businessId } = props;
  const [business] = useSelector(selectBusiness(businessId));
  const [value, setValue] = useControllableValue<OverrideWorkingTimeValue>(props, {
    defaultValue: defaultOverrideWorkingTimeValue,
  });
  const { timeRange, limit, isAvailable, workingArea } = value;

  const [pricingPermission] = useSelector(selectPricingPermission);
  const hasPermission = pricingPermission.enable.has('rotatingSchedule'); // 判断 showWorkingArea，老逻辑重构，确认下这个逻辑，是否需要权限判断

  const isMobileGrooming = business.isMobileGrooming();
  const showWorkingArea = isMobileGrooming && hasPermission;

  const handleCheckboxChange = (checked: boolean) => {
    setValue((pre) => {
      // 如果勾选时 working time 为空，要给一个默认值
      if (checked && !timeRange?.length) {
        return {
          ...pre,
          timeRange: defaultOverrideWorkingTimeValue.timeRange,
          isAvailable: true,
        };
      }

      return {
        ...pre,
        isAvailable: checked,
      };
    });
  };

  const handleTimePeriodChange = (times: WorkingHourTimeRange[]) => {
    setValue((pre) => ({
      ...pre,
      isAvailable: times.length > 0,
      timeRange: times,
    }));
  };

  const handleLimitationChange = (limit: LimitationFormValue) => {
    setValue((pre) => ({
      ...pre,
      limit,
    }));
  };

  const renderOverrideForm = () => {
    if (!isAvailable) return null;

    return (
      <>
        {showWorkingArea && (
          <div className="moe-flex-1">
            <Heading size="5">Service area</Heading>
            <ServiceAreaSelector
              className="!moe-w-[300px] moe-mt-[12px]"
              immediate
              businessId={businessId}
              value={workingArea?.[0]?.areaId}
              onChange={(v) => {
                setValue((pre) => ({
                  ...pre,
                  workingArea: [{ areaId: v }],
                }));
              }}
            />
          </div>
        )}
        <div className="moe-w-[300px]">
          <Heading size="5">Working time</Heading>
          <TimePeriods
            className="moe-relative moe-mt-[12px]"
            minuteStep={MinuteStep}
            value={value.timeRange || []}
            onChange={handleTimePeriodChange}
            onAdd={() =>
              onAdd?.({
                ...value,
                timeRange: [...(value.timeRange || []), { startTime: 0, endTime: OneDayMinutes - 5 }],
              })
            }
            nextPeriodValue={(values) => {
              const last = values[values.length - 1];
              if (!last) return null;

              const { endTime } = last;
              return { startTime: endTime, endTime: OneDayMinutes - 5 };
            }}
            // always can delete
            deletable={() => true}
            addable={(values, index) => {
              const lastIndex = values.length - 1;
              const isLast = lastIndex === index;
              const hasLeftSpace = OneDayMinutes - (values[lastIndex]?.endTime ?? 0) > MinuteStep; // 用=判断，会造成start和end相等
              return isLast && hasLeftSpace;
            }}
          />
        </div>

        <div>
          <Heading size="5">Booking limit</Heading>
          <LimitationActions className="moe-mt-[12px]" limit={limit} onChange={handleLimitationChange} />
        </div>
      </>
    );
  };

  return (
    <div className={cn(className, 'moe-flex moe-flex-col moe-gap-m')}>
      <Checkbox isSelected={isAvailable} onChange={handleCheckboxChange}>
        Set selected date(s) as working date(s)
      </Checkbox>
      {renderOverrideForm()}
    </div>
  );
}
