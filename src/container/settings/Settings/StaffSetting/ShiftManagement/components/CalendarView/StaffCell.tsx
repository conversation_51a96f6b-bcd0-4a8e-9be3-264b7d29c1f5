import { MinorEditOutlined } from '@moego/icons-react';
import { IconButton, Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import React, { useCallback, useMemo } from 'react';
import {
  currentWorkingHourWeekBox,
  editingWorkingHourStaffIdBox,
  isWithInCycleWeek,
  staffScheduleViewTypeBox,
} from '../../../../../../../store/staffSchedule/staffSchedule.boxes';
import {
  type AvailabilityType,
  MaxStaffEndDate,
  StaffWorkingHourViewType,
  WeekKeyMapNum,
} from '../../../../../../../store/staffSchedule/staffSchedule.types';
import { StaffPhotoSticker } from '../../components/StaffPhotoSticker';
import { type EnumValues } from '../../../../../../../store/utils/createEnum';
import { selectCalendarViewData } from '../../../../../../../store/staffSchedule/staffSchedule.selectors';
import { getRotatingWeekMeta } from '../../utils/rotatingWeek';

export interface StaffCellProps {
  availabilityType: EnumValues<typeof AvailabilityType>;
  staffId: number;
}

export function StaffCell(props: StaffCellProps) {
  const { staffId, availabilityType } = props;
  const dispatch = useDispatch();
  const [currentWeek, calendarViewData] = useSelector(
    currentWorkingHourWeekBox,
    selectCalendarViewData(staffId, availabilityType),
  );
  const { scheduleType, startDate } = calendarViewData;
  const isEveryWeek = scheduleType === WeekKeyMapNum.firstWeek;
  const currentWeekOneDayInCycleWeek = useMemo(
    () =>
      new Array(7)
        .fill(0)
        .some((_, index) => isWithInCycleWeek(currentWeek.add(index, 'day'), startDate, MaxStaffEndDate, scheduleType)),
    [currentWeek, startDate, scheduleType],
  );

  const weekLabel = useMemo(() => {
    const { weekNum } = getRotatingWeekMeta({
      date: currentWeek,
      scheduleType,
      startDate,
    });

    return isEveryWeek ? 'Every week' : `Week ${weekNum + 1} of ${scheduleType}`;
  }, [currentWeek, scheduleType, startDate, isEveryWeek]);

  const go2ListView = useCallback(() => {
    dispatch([
      editingWorkingHourStaffIdBox.setState(staffId),
      staffScheduleViewTypeBox.setState(StaffWorkingHourViewType.ListView),
    ]);
  }, [staffId]);

  return (
    <StaffPhotoSticker
      disableInteractive
      key={staffId}
      staffId={staffId}
      className="!moe-flex !moe-gap-[8px] !moe-select-none"
      staffClassName="!moe-font-bold !moe-text-[14px] !moe-text-[#333] !moe-leading-[18px] !moe-truncate"
      extra={
        currentWeekOneDayInCycleWeek && (
          <Text variant="caption" className="moe-mt-xs moe-text-secondary">
            {weekLabel}
          </Text>
        )
      }
      right={
        <IconButton
          onPress={go2ListView}
          icon={<MinorEditOutlined />}
          tooltip="Set regular shifts"
          color="transparent"
        />
      }
    />
  );
}
