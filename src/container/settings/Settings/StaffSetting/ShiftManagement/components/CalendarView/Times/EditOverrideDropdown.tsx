import { MinorTrashOutlined } from '@moego/icons-react';
import { Dropdown } from '@moego/ui';
import { type Dayjs } from 'dayjs';
import React, { useCallback } from 'react';
import IconCalendarCheck from '../../../../../../../../assets/svg/calendar-schedule-check.svg';
import { SvgIcon } from '../../../../../../../../components/Icon/Icon';
import { type WorkingHourValue, type ServiceAreaRange } from '../../../../../../../../store/staff/staff.boxes';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../../../utils/DateTimeUtil';
import { getDefaultLimit } from '../../Limitation/Limitation.type';
import { useStaffScheduleDrawerContext } from '../../StaffSchedule/StaffScheduleDrawerContext';

export interface EditOverrideDropdownProps {
  disabled?: boolean;
  info: {
    staffId: number;
    date: Dayjs;
    value: WorkingHourValue;
    area: ServiceAreaRange[];
    isOverride: boolean;
  };
  onDeleteOverride?: (id: string) => void;
  children?: React.ReactElement;
  businessId: number;
}

export function EditOverrideDropdown(props: EditOverrideDropdownProps) {
  const {
    children,
    info: { staffId, date, area, value, isOverride },
    onDeleteOverride,
    disabled,
    businessId,
  } = props;
  const onlyOneOperationAdd = !isOverride;
  const { openAddOverrideDrawer } = useStaffScheduleDrawerContext();

  const timeData = value.timeRange;

  const addOverrideHandler = useCallback(() => {
    if (disabled) {
      return;
    }
    openAddOverrideDrawer({
      defaultDate: date.format(DATE_FORMAT_EXCHANGE),
      staffId,
      businessId,
      value: {
        dates: [date],
        value: {
          isAvailable: timeData.length > 0,
          timeRange: timeData,
          limit: getDefaultLimit(),
        },
        workingArea: [...(area || [])],
      },
    });
  }, [disabled, openAddOverrideDrawer, date, staffId, businessId, timeData, area]);

  return (
    <>
      {onlyOneOperationAdd ? (
        React.cloneElement(children!, { onClick: addOverrideHandler })
      ) : (
        <Dropdown sideOffset={-20} isDismissable={true} isDisabled={disabled}>
          <Dropdown.Trigger>{children!}</Dropdown.Trigger>
          <Dropdown.Menu
            onAction={(k) => {
              if (k === 'edit') {
                if (isOverride) {
                  openAddOverrideDrawer({
                    defaultDate: date.format(DATE_FORMAT_EXCHANGE),
                    staffId,
                    businessId,
                    value: {
                      dates: [date],
                      value,
                      workingArea: [...(area || [])],
                    },
                  });
                } else {
                  addOverrideHandler();
                }
              }

              if (k === 'delete') {
                onDeleteOverride?.(date.format('YYYY-MM-DD'));
              }
            }}
          >
            <Dropdown.Item key="edit" icon={<SvgIcon src={IconCalendarCheck} size={20} color="#3D414B" />}>
              Edit date override
            </Dropdown.Item>
            <Dropdown.Item key="delete" icon={<MinorTrashOutlined />}>
              Delete date override
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      )}
    </>
  );
}
