import React from 'react';
import { StaffCell } from './StaffCell';
import { AvailabilityType } from '../../../../../../../store/staffSchedule/staffSchedule.types';
import cn from 'classnames';
import { FullWeekDayList } from '../../../../../../../store/onlineBooking/models/OnlineBookingPreference';
import { type Dayjs } from 'dayjs';
import { TimeCell } from './Times/TimeCell';
import { SlotCell } from './Slots/SlotCell';
import { type EnumValues } from '../../../../../../../store/utils/createEnum';

export interface CalendarRowProps {
  staffId: number;
  startWeekDate: Dayjs;
  index: number;
  rowSize: number;
  businessId: number;
  availabilityType: EnumValues<typeof AvailabilityType>;
}

export const CalendarRow = (props: CalendarRowProps) => {
  const { staffId, startWeekDate, index, rowSize, businessId, availabilityType } = props;

  return (
    <div
      className={cn(
        'moe-flex moe-border-divider hover:moe-bg-neutral-sunken-0 moe-group/wrap moe-pr-spacing-xxs moe-border-b',
      )}
    >
      <div className="moe-w-[200px] moe-flex-shrink-0 moe-py-s moe-pr-s moe-pl-xs">
        <StaffCell staffId={staffId} availabilityType={availabilityType} />
      </div>
      <div className="moe-flex-1">
        <div key={staffId} className="moe-grid moe-grid-cols-7 moe-gap-s">
          {FullWeekDayList.map((weekDay, weekIndex) => {
            return (
              <div className="moe-min-w-0" key={weekDay}>
                {availabilityType === AvailabilityType.BY_TIME ? (
                  <TimeCell
                    staffId={staffId}
                    date={startWeekDate.add(weekIndex, 'day')}
                    rowIndex={index}
                    rowSize={rowSize}
                    businessId={businessId}
                  />
                ) : (
                  <SlotCell
                    staffId={staffId}
                    date={startWeekDate.add(weekIndex, 'day')}
                    rowIndex={index}
                    rowSize={rowSize}
                    businessId={businessId}
                  />
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
