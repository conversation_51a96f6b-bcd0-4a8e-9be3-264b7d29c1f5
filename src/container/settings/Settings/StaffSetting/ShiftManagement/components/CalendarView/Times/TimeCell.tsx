import { useDispatch, useSelector } from 'amos';
import { message } from 'antd';
import classNames from 'classnames';
import dayjs, { type Dayjs } from 'dayjs';
import React, { useMemo } from 'react';
import IconPlus from '../../../../../../../../assets/svg/icon-plus.svg';
import { SvgIcon } from '../../../../../../../../components/Icon/Icon';
import { deleteStaffDateOverride } from '../../../../../../../../store/staffSchedule/staffSchedule.actions';
import {
  CalendarViewDataRecord,
  calendarViewDataMapBox,
  staffScheduleDateOverrideMapBox,
} from '../../../../../../../../store/staffSchedule/staffSchedule.boxes';
import { DATE_FORMAT_EXCHANGE } from '../../../../../../../../utils/DateTimeUtil';
import { id2NumberAndRequired } from '../../../../../../../../utils/api/api';
import { useAllBizClosedSituation } from '../../../hooks/useCheckBizOpen';
import { BizOpenStatus } from '../BizOpenStatus';
import { EditOverrideDropdown } from './EditOverrideDropdown';
import { NotAllowCell } from '../NotAllowCell';
import { TimeRangeEntry } from './TimeRangeEntry';
import { NotWorkingCell } from '../NotWorkingCell';
import { getDefaultLimit } from '../../Limitation/Limitation.type';
import { useSerialCallback } from '@moego/tools';

export interface TimesProps {
  staffId: number;
  rowIndex: number;
  rowSize: number;
  date: Dayjs;
  businessId: number;
}

const commonCls = '!moe-font-medium !moe-text-[12px] moe-w-full moe-h-full';

export function TimeCell(props: TimesProps) {
  const { staffId, date, rowIndex, rowSize, businessId } = props;

  const dispatch = useDispatch();
  const [calendarViewData, overrideData] = useSelector(
    calendarViewDataMapBox.mustGetItem(CalendarViewDataRecord.ownKey(businessId, staffId)),
    staffScheduleDateOverrideMapBox.mustGetItem(staffId),
  );

  const bizClosed = useAllBizClosedSituation(date, businessId);

  const dateKey = date.format(DATE_FORMAT_EXCHANGE);
  const isFirstRow = rowIndex === 0;
  const isLastRow = rowIndex === rowSize - 1;
  const isEven = rowSize % 2 === 0;
  const disabled = useMemo(() => date < dayjs().startOf('date'), [date]);

  const workingHourValue = calendarViewData.workingHour[dateKey] || {
    isAvailable: false,
    timeRange: [],
    limit: getDefaultLimit(),
  };

  const overrideDateData = useMemo(() => {
    const allOverrides = [...(overrideData.history || []), ...(overrideData.ongoing || [])];
    return allOverrides.find((item) => item.overrideDate === dateKey);
  }, [overrideData.history, overrideData.ongoing, dateKey]);

  const area = id2NumberAndRequired(calendarViewData.area[dateKey]);
  const overrideArea = calendarViewData.overrideArea[dateKey];

  const mergedWorkingHourValue = overrideDateData?.value || workingHourValue;
  const hasOverride = Boolean(overrideDateData || overrideArea); // 理论上如果有 overrideArea，就一定应该有 overrideDateDate
  const overrideNotWorking = hasOverride && !mergedWorkingHourValue?.isAvailable; // 这种就是 override 成了一个not working
  const { timeRange, isAvailable } = mergedWorkingHourValue;
  const editInfo = { date, staffId, area, value: mergedWorkingHourValue, isOverride: hasOverride };

  const onDelete = useSerialCallback(async (day: string) => {
    await dispatch(deleteStaffDateOverride(day, staffId));
    message.success('Date override has been deleted successfully.');
  });

  return !Array.isArray(bizClosed) ? (
    <div className={classNames(commonCls)}>
      <NotAllowCell
        isFirstRow={isFirstRow}
        isLastRow={isLastRow}
        className={
          isFirstRow
            ? classNames('!moe-flex-1 !moe-flex !moe-justify-center', isEven ? '!moe-pt-[20px]' : '!moe-items-center')
            : undefined
        }
      >
        {isFirstRow && <BizOpenStatus label={bizClosed.label} msg={bizClosed.msg} />}
      </NotAllowCell>
    </div>
  ) : overrideNotWorking ? (
    <EditOverrideDropdown
      disabled={disabled}
      info={editInfo}
      onDeleteOverride={async (day) => {
        if (onDelete.isBusy()) {
          return;
        }
        await onDelete(day);
      }}
      businessId={businessId}
    >
      <NotWorkingCell isDisabled={disabled} />
    </EditOverrideDropdown>
  ) : isAvailable && timeRange?.length ? (
    <div
      className={classNames(
        commonCls,
        disabled ? '!moe-cursor-default moe-opacity-50' : 'moe-group !moe-cursor-pointer',
      )}
    >
      <EditOverrideDropdown
        disabled={disabled}
        info={editInfo}
        onDeleteOverride={async (id) => {
          if (onDelete.isBusy()) {
            return;
          }
          await onDelete(id);
        }}
        businessId={businessId}
      >
        <div className="!moe-h-full !moe-py-[8px] !moe-pb-[12px] !moe-flex !moe-flex-col !moe-gap-y-[4px]">
          <TimeRangeEntry
            disabled={disabled}
            isOverride={hasOverride}
            date={date}
            staffId={staffId}
            ranges={timeRange}
            area={area}
            businessId={businessId}
          />
        </div>
      </EditOverrideDropdown>
    </div>
  ) : (
    <EditOverrideDropdown info={editInfo} disabled={disabled} businessId={businessId}>
      <div
        className={classNames(
          commonCls,
          '!moe-pt-[8px] !moe-pb-[12px] moe-group moe-min-h-[62px]',
          disabled ? '!moe-cursor-not-allowed' : 'moe-group !moe-cursor-pointer',
        )}
      >
        <div
          className={classNames(
            disabled ? '' : 'group-hover:!moe-flex',
            '!moe-hidden !moe-justify-center !moe-items-center !moe-bg-white !moe-h-full !moe-rounded-[8px]',
          )}
        >
          <SvgIcon src={IconPlus} size={16} color="#F96B18" />
        </div>
      </div>
    </EditOverrideDropdown>
  );
}
