import { useDispatch, useSelector } from 'amos';
import React, { useCallback, useEffect, useRef } from 'react';
import { getClosedDateList } from '../../../../../../../store/business/closedDate.actions';
import { getHolidays } from '../../../../../../../store/business/holiday.actions';
import { defaultAreaId } from '../../../../../../../store/serviceArea/serviceArea.boxes';
import {
  getAllSlotOverride,
  getCalendarViewData,
  getCalendarViewSlotData,
  getStaffDateOverride,
  toggleCalendarServiceArea,
} from '../../../../../../../store/staffSchedule/staffSchedule.actions';
import { currentWorkingHourWeekBox } from '../../../../../../../store/staffSchedule/staffSchedule.boxes';
import { id2NumberAndRequired } from '../../../../../../../utils/api/api';
import { SwitchWeek } from '../../components/SwitchWeek';
import { RowWeek } from './RowWeek';
import { AvailabilityType } from '../../../../../../../store/staffSchedule/staffSchedule.types';
import { type EnumValues } from '../../../../../../../store/utils/createEnum';
import { CalendarBody } from './CalendarBody';
import { isNormal } from '../../../../../../../store/utils/identifier';
import { useAsync } from 'react-use';
import { Spin } from '@moego/ui';

export interface CalendarViewProps {
  businessId: number;
  availabilityType: EnumValues<typeof AvailabilityType>;
}

export function CalendarView(props: CalendarViewProps) {
  const { businessId, availabilityType } = props;
  const dispatch = useDispatch();
  const [selectedWeek] = useSelector(currentWorkingHourWeekBox);
  const countRef = useRef<{
    [bizId: number]: number;
  }>({});

  const getWorkingHourData = useCallback(async () => {
    const { area } = await dispatch(getCalendarViewData(selectedWeek));
    await dispatch(getStaffDateOverride());

    if (!countRef.current[businessId]) {
      if (area) {
        // 如果有自定义服务区域，默认勾选，仅仅判断第一个week，接下来的fetch不再判断
        const hasCustomServiceArea = id2NumberAndRequired(area).some((a) =>
          Object.values(a.workingAreaRange).some((i) => i[0] && i[0].areaId !== defaultAreaId),
        );
        dispatch(toggleCalendarServiceArea('serviceArea', hasCustomServiceArea));
      }
      dispatch(toggleCalendarServiceArea('workingHour', true));
    }

    countRef.current[businessId]++;
  }, [businessId, dispatch, selectedWeek]);

  const getWorkingSlotData = useCallback(async () => {
    return dispatch([getCalendarViewSlotData(selectedWeek), getAllSlotOverride()]);
  }, [dispatch, selectedWeek]);

  useEffect(() => {
    if (isNormal(businessId)) {
      dispatch([getHolidays(businessId), getClosedDateList(businessId)]);
    }
  }, [businessId, dispatch]);

  const { loading } = useAsync(async () => {
    if (!selectedWeek) return;

    if (availabilityType === AvailabilityType.BY_SLOT) {
      await getWorkingSlotData();
    } else {
      await getWorkingHourData();
    }
  }, [availabilityType, selectedWeek, getWorkingSlotData, getWorkingHourData, dispatch]);

  return (
    <div className="moe-relative">
      <div className="moe-bg-white moe-sticky moe-top-[-32px] moe-z-10">
        <div className="moe-pb-[24px] moe-pt-[16px]">
          <SwitchWeek />
        </div>
        <RowWeek />
      </div>

      <Spin isLoading={loading}>
        <CalendarBody businessId={businessId} availabilityType={availabilityType} />
      </Spin>
    </div>
  );
}
