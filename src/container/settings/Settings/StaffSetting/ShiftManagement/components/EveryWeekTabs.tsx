import { Tabs } from '@moego/ui';
import dayjs, { type Dayjs } from 'dayjs';
import { range } from 'lodash';
import React, { type PropsWithChildren } from 'react';
import {
  MaxStaffEndDate,
  StaffScheduleType,
  WeekKeyMapNum,
} from '../../../../../../store/staffSchedule/staffSchedule.types';
import { useControllableValue } from '../../../../../../utils/hooks/useControlledValue';
import { getRotatingWeekMeta } from '../utils/rotatingWeek';

interface EveryWeekTabsProps {
  className?: string;
  scheduleType?: WeekKeyMapNum;
  // startDate, endDate 用于计算 rotate week
  dateScope?: [Dayjs, Dayjs];
  // active tab value
  value?: number;
  onChange?: (weekType: number) => void;
  children: React.ReactNode | ((activeWeekType: number) => React.ReactNode);
}

const defaultDateScope: [Dayjs, Dayjs] = [dayjs(), MaxStaffEndDate];

/**
 * Staff Schedule 业务组件，自动判断 schedule 是否需要渲染 tabs
 * 1. scheduleType -> 决定 tabs 数量
 * 2. activeWeekType -> 当前激活的 tab
 * @param props schedule 包含 active tab 信息
 * @returns
 */
export const EveryWeekTabs = (props: PropsWithChildren<EveryWeekTabsProps>) => {
  const {
    onChange,
    children,
    className,
    scheduleType = StaffScheduleType.Every1Week,
    value,
    dateScope = defaultDateScope,
  } = props;

  const [activeWeekType, setActiveWeekType] = useControllableValue<number>({
    value,
    defaultValue: WeekKeyMapNum.firstWeek,
    onChange,
  });

  const { weekNum } = getRotatingWeekMeta({
    date: dayjs(),
    scheduleType,
    startDate: dateScope[0],
    endDate: dateScope[1],
  });
  const weekType = scheduleType;
  const repeats = range(weekType);

  return (
    <div className={className}>
      {StaffScheduleType.mapKeys[weekType] !== 'Every1Week' ? (
        <Tabs
          selectedKey={String(activeWeekType)}
          onChange={(v) => {
            setActiveWeekType(+v);
          }}
        >
          {repeats.map((num) => {
            const isCurrentWeek = num === weekNum;
            const whichWeek = num + 1;

            return (
              <Tabs.Item
                key={String(whichWeek)}
                label={
                  <div className="!moe-py-[1px]">{`Week ${whichWeek} of ${repeats.length}${isCurrentWeek ? ' (current)' : ''}`}</div>
                }
              />
            );
          })}
        </Tabs>
      ) : null}
      {typeof children === 'function' ? children(activeWeekType) : children}
    </div>
  );
};
