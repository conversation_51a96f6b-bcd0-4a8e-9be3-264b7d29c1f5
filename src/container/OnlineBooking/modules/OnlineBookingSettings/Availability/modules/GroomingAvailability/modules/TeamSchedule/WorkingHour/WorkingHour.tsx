import React from 'react';
import { useDispatch, useSelector } from 'amos';
import {
  OBStaffWorkingHourMapBox,
  syncWithStaffRegularWorkingHour,
} from '../../../../../../../../../../store/onlineBooking/settings/availabilityGrooming.boxes';
import { WeekTimeSchedule } from '../../../../../../../../../settings/Settings/StaffSetting/ShiftManagement/components/StaffSchedule/WorkingHourV2/WeekTimeSchedule';
import type { StaffRecord } from '../../../../../../../../../../store/staff/staff.boxes';
import { selectStaffWeekTimeAt } from '../../../../../../../../../../store/staffSchedule/staffSchedule.selectors';
import dayjs from 'dayjs';

export interface WorkingHourProps {
  className?: string;
  staff: StaffRecord;
  isDisabled?: boolean;
}

export const WorkingHour = (props: WorkingHourProps) => {
  const { staff, isDisabled, className } = props;
  const dispatch = useDispatch();
  const [OBStaffWorkingHour, SMStaffWorkingHour, isSynced] = useSelector(
    OBStaffWorkingHourMapBox.mustGetItem(staff.id),
    selectStaffWeekTimeAt(staff.id, dayjs()),
    syncWithStaffRegularWorkingHour,
  );

  return (
    <WeekTimeSchedule
      showServiceArea={false}
      className={className}
      value={isSynced ? SMStaffWorkingHour : OBStaffWorkingHour}
      isDisabled={isDisabled}
      onChange={(v) => {
        dispatch(OBStaffWorkingHourMapBox.mergeItem(staff.id, v));
      }}
    />
  );
};
