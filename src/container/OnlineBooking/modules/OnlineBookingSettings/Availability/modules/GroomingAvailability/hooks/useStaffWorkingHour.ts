import { useDispatch, useSelector } from 'amos';
import { useEffect } from 'react';
import { selectBusinessStaffs } from '../../../../../../../../store/staff/staff.selectors';
import {
  getStaffDateOverride,
  getStaffWorkingHour,
} from '../../../../../../../../store/staffSchedule/staffSchedule.actions';
import { isNormal } from '../../../../../../../../store/utils/identifier';

export function useStaffWorkingHour(staffId?: number, withOverrides?: boolean) {
  const dispatch = useDispatch();
  const [staffIdList] = useSelector(selectBusinessStaffs());

  useEffect(() => {
    if (isNormal(staffId)) {
      dispatch(getStaffWorkingHour([staffId]));
      if (withOverrides) {
        dispatch(getStaffDateOverride([staffId]));
      }
    } else {
      dispatch(getStaffWorkingHour(staffIdList.toArray()));
    }
  }, [staffId, staffIdList, withOverrides, dispatch]);
}
