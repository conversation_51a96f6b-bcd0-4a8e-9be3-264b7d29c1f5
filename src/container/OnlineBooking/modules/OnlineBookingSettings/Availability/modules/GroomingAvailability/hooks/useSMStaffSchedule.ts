import { useDispatch, useSelector } from 'amos';
import { useCallback, useEffect } from 'react';
import { selectBusinessStaffs } from '../../../../../../../../store/staff/staff.selectors';
import {
  getStaffDateOverride,
  getStaffSlotOverride,
  getStaffWorkingHour,
  getStaffWorkingSlot,
} from '../../../../../../../../store/staffSchedule/staffSchedule.actions';
import { isNormal } from '../../../../../../../../store/utils/identifier';

export function useSMStaffSchedule(staffId?: number, withOverrides?: boolean) {
  const dispatch = useDispatch();
  const [staffIdList] = useSelector(selectBusinessStaffs());

  const getSMStaffWorkingHour = useCallback(() => {
    if (isNormal(staffId)) {
      dispatch(getStaffWorkingHour([staffId]));
      if (withOverrides) {
        dispatch(getStaffDateOverride([staffId]));
      }
    } else {
      dispatch(getStaffWorkingHour(staffIdList.toArray()));
    }
  }, [dispatch, staffId, staffIdList, withOverrides]);

  const getSMStaffWorkingSlot = useCallback(() => {
    if (isNormal(staffId)) {
      dispatch(getStaffWorkingSlot([staffId]));
      if (withOverrides) {
        dispatch(getStaffSlotOverride([staffId]));
      }
    } else {
      dispatch(getStaffWorkingSlot());
    }
  }, [dispatch, staffId, withOverrides]);

  useEffect(() => {
    getSMStaffWorkingHour();
    getSMStaffWorkingSlot();
  }, [getSMStaffWorkingHour, getSMStaffWorkingSlot]);
}
