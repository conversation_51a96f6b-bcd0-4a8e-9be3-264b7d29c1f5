import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Heading, MajorWarningOutlined, Modal, Text } from '@moego/ui';
import { useSelector } from 'amos';
import { type Dayjs } from 'dayjs';
import React from 'react';
import { Condition } from '../../../components/Condition';
import { Switch } from '../../../components/SwitchCase';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { type PetPlaygroups } from '../../../store/playgroups/playgroups.actions';
import { useAsyncCallback } from '../../../utils/hooks/useAsyncCallback';
import { useFloatableHost } from '../../../utils/hooks/useFloatableHost';

export const useCheckServiceChangesModal = () => {
  const { mountModal } = useFloatableHost<boolean>();
  const [business] = useSelector(selectCurrentBusiness);

  const checkServiceChangesModal = useAsyncCallback(
    (
      params: {
        activeItem: PetPlaygroups;
        originalDate: Dayjs;
        currentDate: Dayjs;
        originalContainerName: string;
        currentContainerName: string;
      },
      onConfirm: () => Promise<void>,
    ) => {
      const { activeItem, originalDate, currentDate, originalContainerName, currentContainerName } = params;
      const { serviceItemTypes, relatedPetNames, petName } = activeItem;
      const isBoarding = serviceItemTypes.includes(ServiceItemType.BOARDING);

      const handleConfirm = async () => {
        // boarding 服务不允许修改
        if (isBoarding) {
          closeModal(false);
          return;
        }

        await onConfirm();
        closeModal(true);
      };

      const { promise, closeFloatable: closeModal } = mountModal(
        <>
          <Modal
            isOpen
            size="s"
            title={
              <div className="moe-flex moe-items-center moe-gap-8px-100">
                <Condition if={isBoarding}>
                  <MajorWarningOutlined className="moe-text-warning" />
                </Condition>
                <Heading size="3">{isBoarding ? 'Boarding restriction' : 'Change service details'}</Heading>
              </div>
            }
            onClose={() => closeModal(false)}
            onCancel={() => closeModal(false)}
            onConfirm={handleConfirm}
            confirmText={isBoarding ? 'OK' : 'Confirm'}
            showCancelButton={!isBoarding}
          >
            <Switch>
              <Switch.Case if={isBoarding}>
                <Text variant="regular">
                  There&apos;s boarding service within this appointment, please edit date in appointment detail if
                  needed.
                </Text>
              </Switch.Case>
              <Switch.Case else>
                <Text variant="regular">
                  Are you sure to make the following changes for {petName}?
                  {relatedPetNames?.length
                    ? `Dates for ${relatedPetNames?.join(',')} from the same appointment will also be changed.`
                    : ''}
                </Text>
                <Text variant="regular" className="moe-flex moe-items-center moe-mt-8px-100">
                  Service date: <Heading size="5">&nbsp;{business.formatDate(originalDate)}</Heading>&nbsp;to&nbsp;
                  <Heading size="5">{business.formatDate(currentDate)}</Heading> .
                </Text>
                <Condition if={originalContainerName !== currentContainerName}>
                  <Text variant="regular" className="moe-flex moe-items-center moe-mt-8px-100">
                    Playgroup assignment: <Heading size="5">&nbsp;{originalContainerName}</Heading>&nbsp;to&nbsp;
                    <Heading size="5">{currentContainerName}</Heading>.
                  </Text>
                </Condition>
              </Switch.Case>
            </Switch>
          </Modal>
        </>,
      );
      return promise;
    },
  );

  return checkServiceChangesModal;
};
