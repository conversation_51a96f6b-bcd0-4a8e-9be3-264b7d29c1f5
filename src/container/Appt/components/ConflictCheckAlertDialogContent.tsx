import { type CheckSaveAppointmentResult } from '@moego/api-web/moego/api/appointment/v1/appointment_checker_api';
import { CompressedAvatar } from '@moego/business-components';
import { Alert, cn, Heading, Text } from '@moego/ui';
import dayjs from 'dayjs';
import { T_MINUTE } from 'monofile-utilities/lib/consts';
import React from 'react';
import type { BusinessRecord } from '../../../store/business/business.boxes';
import { getPetAvatarType } from '../../../utils/BusinessUtil';
import { computeUnits } from '../../../utils/calculator';

interface LodgingOverCapacityAlertProps {
  lodgingUnits: Array<{ name: string; lodgingTypeId: string }>;
  lodgingTypes: Array<{ id: string; name: string }>;
  isShowEditText?: boolean;
}
const LodgingOverCapacityAlert: React.FC<LodgingOverCapacityAlertProps> = ({
  lodgingUnits,
  lodgingTypes,
  isShowEditText,
}) => (
  <Alert color="warning" isBordered title="Lodging over capacity" isCloseable={false}>
    <Text variant="small">
      <Text className="moe-font-bold" color="primary" variant="small" as="span">
        {lodgingUnits
          .map(({ name, lodgingTypeId }) => `${name}(${lodgingTypes.find(({ id }) => id === lodgingTypeId)?.name})`)
          .join(', ')}
      </Text>
      <Text variant="small" as="span">
        {' '}
        {computeUnits(lodgingUnits.length, 'is', 'are')[1]} over capacity.
        {isShowEditText && ' Please edit if needed.'}
      </Text>
    </Text>
  </Alert>
);

interface BusinessClosedAlertProps {
  closedDate: string[];
  business: BusinessRecord;
  isShowEditText?: boolean;
}
const BusinessClosedAlert: React.FC<BusinessClosedAlertProps> = ({ closedDate, business, isShowEditText }) => (
  <Alert color="warning" isBordered title="Business closed date" isCloseable={false}>
    <Text variant="small">
      <Text className="moe-font-bold" color="primary" variant="small" as="span">
        {closedDate.map((d) => dayjs(d).format(business.dateFormatMD)).join(', ')}
      </Text>
      <Text variant="small" as="span">
        {' '}
        {computeUnits(closedDate.length, 'is', 'are')[1]} business closed date(s) based on your settings.
        {isShowEditText && ' Please edit if needed.'}
      </Text>
    </Text>
  </Alert>
);

const ConflictAppointmentsAlert: React.FC<{
  conflictAppointments: CheckSaveAppointmentResult['appointmentDateConflictCheckResult']['conflictAppointments'];
  business: BusinessRecord;
}> = ({ conflictAppointments, business }) => {
  return (
    <>
      <Alert color="warning" isBordered title="Conflicting appointments" isCloseable={false}>
        <Text variant="small">Pets below have conflicting appointments within the scheduled date.</Text>
      </Alert>

      {conflictAppointments.map(({ pet, appointments }) => (
        <div key={pet.id} className="moe-p-s moe-pt-[8px] moe-border-divider moe-border moe-rounded-m">
          <div className="moe-flex moe-items-center moe-gap-s moe-p-xs moe-rounded-m moe-bg-neutral-sunken-0">
            <CompressedAvatar.Pet
              src={pet.avatarPath}
              className="moe-rounded-[48px]"
              type={getPetAvatarType(pet.petType)}
              size="m"
            />
            <div>
              <Heading size="5">{pet.petName}</Heading>
              <Text className="moe-text-tertiary" variant="small">
                {[pet.breed, pet.weight, pet.coatType].filter(Boolean).join(' · ')}
              </Text>
            </div>
          </div>

          {appointments.map(
            ({ appointmentId, services, startDate, endDate, appointmentStartTime, appointmentEndTime }) => {
              const startDay = dayjs(new Date(startDate.year, startDate.month - 1, startDate.day));
              const endDay = dayjs(new Date(endDate.year, endDate.month - 1, endDate.day));
              const isSameDay = startDay.isSame(endDay, 'day');

              const showStartTime = `${startDay.format('ddd')}, ${startDay.format(business.dateFormatMD)} ${business.formatFixedTime(appointmentStartTime * T_MINUTE)}`;
              const showEndTime = isSameDay
                ? `${business.formatFixedTime(appointmentEndTime * T_MINUTE)}`
                : `${endDay.format('ddd')}, ${endDay.format(business.dateFormatMD)} ${business.formatFixedTime(appointmentEndTime * T_MINUTE)}`;

              return (
                <React.Fragment key={appointmentId}>
                  <Text className="moe-mt-s moe-text-primary moe-font-bold" variant="small">
                    {services.map(({ serviceName }) => serviceName).join(', ')}
                  </Text>
                  <Text variant="small">
                    {showStartTime} - {showEndTime}
                  </Text>
                </React.Fragment>
              );
            },
          )}
        </div>
      ))}
    </>
  );
};

interface ConflictCheckAlertDialogContentProps extends CheckSaveAppointmentResult {
  business: BusinessRecord;
  isShowEditText?: boolean;
  className?: string;
}

export const ConflictCheckAlertDialogContent: React.FC<ConflictCheckAlertDialogContentProps> = (props) => {
  const {
    lodgingOverCapacityCheckResult = { lodgingUnits: [], lodgingTypes: [] },
    appointmentDateConflictCheckResult = { conflictAppointments: [] },
    businessClosedDateCheckResult = { closedDate: [] },
    business,
    isShowEditText = true,
    className,
  } = props;

  return (
    <div className={cn('moe-flex moe-flex-col moe-gap-s', className)}>
      {lodgingOverCapacityCheckResult.lodgingUnits.length > 0 && (
        <LodgingOverCapacityAlert {...lodgingOverCapacityCheckResult} isShowEditText={isShowEditText} />
      )}

      {businessClosedDateCheckResult.closedDate.length > 0 && (
        <BusinessClosedAlert
          closedDate={businessClosedDateCheckResult.closedDate}
          business={business}
          isShowEditText={isShowEditText}
        />
      )}

      {appointmentDateConflictCheckResult.conflictAppointments.length > 0 && (
        <ConflictAppointmentsAlert
          conflictAppointments={appointmentDateConflictCheckResult.conflictAppointments}
          business={business}
        />
      )}
    </div>
  );
};
