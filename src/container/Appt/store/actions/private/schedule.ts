import { ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type Action, type Mutation, action } from 'amos';
import dayjs from 'dayjs';
import { isNumber } from 'lodash';
import { currentBusinessIdBox } from '../../../../../store/business/business.boxes';
import { DAY_MAX_END_MINS } from '../../../../../store/calendarLatest/calendar.boxes';
import { serviceMapBox } from '../../../../../store/service/service.boxes';
import { bizScheduleMapBox } from '../../../../../store/staffSchedule/staffSchedule.boxes';
import { type PartialRequired } from '../../../../../store/utils/RecordMap';
import { transformTimeDateToDate } from '../../../../Calendar/Lodging/hooks/useBizSchedule';
import { ApptServiceRecord, apptAddOnMapBox, apptServiceMapBox } from '../../appt.boxes';
import {
  selectApptPetAddon,
  selectApptPetService,
  selectMainServiceInAppt,
  selectPetsInAppt,
} from '../../appt.selectors';
import {
  type ApptAddOnInfoRecord,
  type ApptServiceInfoRecord,
  type ApptServiceInfoRecordTimeKeys,
  type ServiceAddOnDateInfo,
} from '../../appt.types';
import { addDateOffset } from '../../appt.utils';
import { AppointmentCheckerClient } from '../../../../../middleware/clients';
import {
  type CheckLodgingOverCapacityParams,
  type GetAvailableDatesParams,
  type GetEvaluationAvailableTimeParams,
} from '@moego/api-web/moego/api/appointment/v1/appointment_checker_api';
import { isNormal } from '../../../../../store/utils/identifier';
import { getLodgingUnitId } from '../../../../../store/calendarLatest/calendar_data.utils';

export const setApptAddon = action(
  async (dispatch, select, ownerId: string, input: Partial<Omit<ApptAddOnInfoRecord, 'dateType'>>) => {
    dispatch(apptAddOnMapBox.mergeItem(ownerId, input));
  },
);

export const setApptService = action(
  async (dispatch, select, ownerId: string, input: Partial<ApptServiceInfoRecord>) => {
    const data = select(apptServiceMapBox.mustGetItem(ownerId)).toJSON();
    dispatch(apptServiceMapBox.setItem(ownerId, new ApptServiceRecord({ ...data, ...input })));
  },
);

export const syncApptMainService = action(
  async (dispatch, select, appointmentId: string, ownerId: string, input: Partial<ApptServiceInfoRecord>) => {
    const service = select(selectMainServiceInAppt(appointmentId));
    const isMainService = service.ownId === ownerId;
    const timeKeys = ['startDate', 'endDate', 'startTime', 'endTime'];
    const isModifyTime = timeKeys.some((key) => Object.prototype.hasOwnProperty.call(input, key));

    // 如果修改了主service的时间，则检查下其他service的时间
    if (isMainService && isModifyTime) {
      const nextState = timeKeys.reduce(
        (acc, curKey) => {
          if (Object.prototype.hasOwnProperty.call(input, curKey)) {
            const value: any = input[curKey as unknown as ApptServiceInfoRecordTimeKeys]!;
            acc[curKey as unknown as ApptServiceInfoRecordTimeKeys] = value;
          }
          return acc;
        },
        {} as Partial<ApptServiceInfoRecord>,
      );
      dispatch(batchApptServiceTime(appointmentId, ownerId, nextState));
    }
  },
);

export const syncApptService = action(
  async (dispatch, select, appointmentId: string, ownerId: string, input: Partial<ApptServiceInfoRecord>) => {
    dispatch(setApptService(ownerId, input));
    dispatch(syncApptMainService(appointmentId, ownerId, input));
  },
);

/** 限制service和addon的时间范围 */
export const batchApptServiceTime = action(
  async (dispatch, select, appointmentId: string, mainServiceOwnId: string, input: Partial<ApptServiceInfoRecord>) => {
    const pets = select(selectPetsInAppt(appointmentId));
    const actions: Mutation<any, any, any>[] = [];
    const { startDate, endDate, startTime, endTime } = input;
    const mainService = select(apptServiceMapBox.mustGetItem(mainServiceOwnId));
    const min = dayjs(startDate || mainService.startDate).setMinutes(startTime || mainService.startTime || 0);
    const max = dayjs(endDate || mainService.endDate).setMinutes(endTime || mainService.endTime || 0);
    const notInDate = (date?: string) => {
      if (!date) {
        return false;
      }
      const target = dayjs(date).setMinutes(0);
      return target.isBefore(min) || target.isAfter(max);
    };
    const notInTime = (time?: number) => {
      if (!isNumber(time)) {
        return false;
      }
      return time < min.getMinutes() || time > max.getMinutes();
    };
    const businessId = select(currentBusinessIdBox);
    const { timeData } = select(bizScheduleMapBox.mustGetItem(businessId));
    const transformBizWorkingHour = transformTimeDateToDate(timeData);
    const workingEndHoursList = transformBizWorkingHour[dayjs(endDate || startDate).day()] || [];
    const workingEndHours = workingEndHoursList[workingEndHoursList.length - 1]?.endTime || DAY_MAX_END_MINS - 1;

    for (const pet of pets) {
      const { services } = pet;
      for (const service of services) {
        const { serviceType, id } = service;
        if (serviceType === ServiceType.ADDON) {
          const { startDate, startTime, ownId } = select(selectApptPetAddon(appointmentId, id));
          const obj: Partial<ApptAddOnInfoRecord> = {};
          if (!!startDate && notInDate(startDate)) {
            obj.startDate = input.startDate!;
          }
          if (startDate === input.startDate && notInTime(startTime)) {
            obj.startTime = input.startTime;
          }
          actions.push(apptAddOnMapBox.mergeItem(ownId, obj));
        } else {
          const { startDate, endDate, serviceId, startTime, serviceTime, endTime, ownId } = select(
            selectApptPetService(appointmentId, id),
          );
          if (mainServiceOwnId === ownId) {
            continue;
          }
          const { maxDuration } = select(serviceMapBox.mustGetItem(Number(serviceId)));

          const obj: Partial<ApptServiceInfoRecord> = {};
          if (input.startDate && notInDate(startDate)) {
            obj.startDate = input.startDate!;
          }
          if (input.endDate && notInDate(endDate)) {
            obj.endDate = input.endDate!;
          }
          if (isNumber(input.startTime) && notInTime(startTime)) {
            obj.startTime = input.startTime;

            if (!('endTime' in input)) {
              // 选择了 startTime 需要同步计算其他 service 的 endTime, 并且不能超过 workingEndHours
              obj.endTime = Math.min(input.startTime + (serviceTime || maxDuration), workingEndHours);
            }
          }
          // 需要注意 service和addon 都要在main service之间
          if (input.endDate && isNumber(input.endDate) && endDate === input.endDate && notInTime(endTime)) {
            obj.endTime = input.endTime;
          }
          actions.push(apptServiceMapBox.mergeItem(ownId, obj));
        }
      }
    }
    dispatch(actions);
  },
);

export const savePetsServiceAddonDates = action(async (dispatch, select, dates?: ServiceAddOnDateInfo[]) => {
  const actions: Action<Promise<void>, any>[] = [];
  for (const item of dates || []) {
    const { ownId, serviceId, serviceType, ...rest } = item;
    if (serviceType === ServiceType.SERVICE) {
      actions.push(setApptService(ownId, rest));
    } else {
      actions.push(setApptAddon(ownId, rest));
    }
  }
  dispatch(actions);
});

export const updateApptAllServiceDateWithOffset = action(
  (dispatch, select, appointmentId: string, dateOffset: number, nextLodgingId?: string) => {
    const petServiceIdList = select(selectPetsInAppt(appointmentId, false));
    const petServiceList: PartialRequired<ApptServiceInfoRecord, 'ownId'>[] = [];
    const petAddonList: PartialRequired<ApptAddOnInfoRecord, 'ownId'>[] = [];

    petServiceIdList.forEach(({ services }) => {
      services.forEach((service) => {
        const { serviceType, id } = service;
        if (serviceType === ServiceType.SERVICE) {
          const { startDate, endDate, lodgingId, ownId } = select(selectApptPetService(appointmentId, id));
          petServiceList.push({
            ownId,
            startDate: addDateOffset(startDate, dateOffset),
            endDate: addDateOffset(endDate, dateOffset),
            lodgingId: nextLodgingId || lodgingId,
          });
        } else if (serviceType === ServiceType.ADDON) {
          const { startDate, ownId } = select(selectApptPetAddon(appointmentId, id));
          petAddonList.push({ ownId, startDate: addDateOffset(startDate, dateOffset) });
        }
      });
    });
    dispatch([apptServiceMapBox.mergeItems(petServiceList), apptAddOnMapBox.mergeItems(petAddonList)]);
  },
);

export const getAvailableDates = action(async (dispatch, select, param: GetAvailableDatesParams) => {
  const result = await AppointmentCheckerClient.getAvailableDates(param);
  return result;
});

export const getEvaluationAvailableTime = action(async (dispatch, select, param: GetEvaluationAvailableTimeParams) => {
  const result = await AppointmentCheckerClient.getEvaluationAvailableTime(param);
  return result;
});

export const getLodgingOverCapacityInfo = action(
  async (dispatch, select, param: Omit<CheckLodgingOverCapacityParams, 'businessId'>) => {
    const { lodgingUnitIds, lodgingUnitChange, ...rest } = param;
    const businessId = select(currentBusinessIdBox);
    const result = await AppointmentCheckerClient.checkLodgingOverCapacity({
      ...rest,
      businessId: String(businessId),
      lodgingUnitIds: [...new Set(lodgingUnitIds.filter(isNormal))].map(getLodgingUnitId),
      lodgingUnitChange: {
        oldLodgingUnitId: getLodgingUnitId(lodgingUnitChange?.oldLodgingUnitId),
        newLodgingUnitId: getLodgingUnitId(lodgingUnitChange?.newLodgingUnitId),
      },
    });
    return result;
  },
);
