import { type RescheduleBoardingServiceResult } from '@moego/api-web/moego/api/appointment/v1/appointment_schedule_api';
import { MajorWarningOutlined } from '@moego/icons-react';
import { T_MINUTE } from '@moego/reporting';
import { Heading, Modal, Spin, Text } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { type Dayjs } from 'dayjs';
import React, { memo, useEffect, useMemo, useState } from 'react';
import { Condition } from '../../../../../components/Condition';
import { AutoMessageType } from '../../../../../store/autoMessage/autoMessage.boxes';
import { setTicketAlertsProps } from '../../../../../store/calendarLatest/actions/private/calendar.actions';
import {
  rescheduleEvaluationAppt,
  rescheduleLodgingAppt,
} from '../../../../../store/calendarLatest/actions/private/lodgingCalendar.actions';
import {
  selectLodgingCalendarBusiness,
  selectSameApptOtherCardPetList,
} from '../../../../../store/calendarLatest/lodgingCalendar.selectors';
import { lodgingTypeMapBox } from '../../../../../store/lodging/lodgingType.boxes';
import { lodgingUnitMapBox } from '../../../../../store/lodging/lodgingUnit.boxes';
import { useSerialCallback } from '../../../../../utils/hooks/useSerialCallback';
import { ReportActionName } from '../../../../../utils/reportType';
import { reportData } from '../../../../../utils/tracker';
import { type LodgingCardRescheduleInfo } from '../../hooks/useFullCalendarCallback';
import { useRescheduleDaycare } from '../../hooks/useRescheduleDaycare';
import { MedicationRescheduleAlert } from '../../../../Appt/components/EditPetFeedMedication/MedicationRescheduleAlert';
import { DATE_FORMAT_EXCHANGE } from '../../../../../utils/DateTimeUtil';
import { getLodgingOverCapacityInfo } from '../../../../Appt/store/appt.actions';
import { stringToDateMessage } from '../../../../../utils/utils';
import { type CheckLodgingOverCapacityResult } from '@moego/api-web/moego/api/appointment/v1/appointment_checker_api';
import { ConflictCheckAlertDialogContent } from '../../../../Appt/components/ConflictCheckAlertDialogContent';
import { selectCurrentBusiness } from '../../../../../store/business/business.selectors';

export interface RescheduleLodgingApptDatesProps {
  startDate?: Dayjs;
  endDate?: Dayjs;
  className?: string;
  startTime?: number;
  endTime?: number;
}

export const RescheduleLodgingApptDates = memo<RescheduleLodgingApptDatesProps>((props) => {
  const { startDate, endDate, className = '', startTime, endTime } = props;

  const [business] = useSelector(selectLodgingCalendarBusiness);

  const uniqueDateArray = Array.from(
    new Set([
      business.formatDate(startDate) + (startTime && ', ' + business.formatFixedTime(startTime * T_MINUTE, true)),
      business.formatDate(endDate) + (endTime && ', ' + business.formatFixedTime(endTime * T_MINUTE, true)),
    ]),
  );

  return (
    <Heading size="5" className={className}>
      {uniqueDateArray.join(' - ')}
    </Heading>
  );
});

export interface RescheduleLodgingApptModalProps {
  visible: boolean;
  rescheduleInfo: LodgingCardRescheduleInfo;
  dateIsChange?: boolean;
  onClose: () => void;
  onCancel: () => void;
}

export const RescheduleLodgingApptModal = memo<RescheduleLodgingApptModalProps>((props) => {
  const { visible, rescheduleInfo, onClose, onCancel, dateIsChange } = props;

  const { cardId, newStartDate, newEndDate, newLodgingUnitId } = rescheduleInfo;
  const [sameApptOtherCardPetList, lodgingUnitMap, lodgingTypeMap, business] = useSelector(
    selectSameApptOtherCardPetList(cardId),
    lodgingUnitMapBox,
    lodgingTypeMapBox,
    selectCurrentBusiness,
  );

  const {
    hasSameDaycareWillChange,
    normalNewScheduleParams,
    multiDayNewScheduleParams,
    sourceLodgingApptCard,
    evaluationNewScheduleParams,
    isEvaluation,
  } = useRescheduleDaycare(cardId, rescheduleInfo);

  const {
    startDate,
    endDate,
    appointmentId,
    lodgingUnitId,
    petNameList: sourcePetNameList,
    customerId,
    isDaycareApptCard,
    startTime,
    endTime,
  } = sourceLodgingApptCard;

  const dispatch = useDispatch();

  const [{ lodgingOverCapacityCheckResult, businessClosedDateCheckResult }, setCheckResult] =
    useState<CheckLodgingOverCapacityResult>({
      lodgingOverCapacityCheckResult: { lodgingUnits: [], lodgingTypes: [] },
      businessClosedDateCheckResult: { closedDate: [] },
    });
  const [{ conflictServiceNames }, setConflictInfo] = useState<RescheduleBoardingServiceResult>({
    conflictServiceNames: [],
  });
  const dateConflict = conflictServiceNames.length > 0;

  const title = useMemo(
    () =>
      dateConflict ? (
        <div className="moe-flex moe-items-center">
          <MajorWarningOutlined color="#DE921F" />
          <Heading size="3" className="moe-ml-xs moe-text-primary">
            Date conflict
          </Heading>
        </div>
      ) : (
        'Change service details'
      ),
    [dateConflict],
  );

  const { sourceLodgingUnitName, sourceLodgingTypeName, newLodgingUnitName, newLodgingTypeName } = useMemo(() => {
    const { name: sourceLodgingUnitName, lodgingTypeId } = lodgingUnitMap.mustGetItem(lodgingUnitId);
    const sourceLodgingTypeName = lodgingTypeMap.mustGetItem(lodgingTypeId).name;
    const { name: newLodgingUnitName, lodgingTypeId: newLodgingTypeId } = lodgingUnitMap.mustGetItem(
      newLodgingUnitId ?? '',
    );
    const newLodgingTypeName = lodgingTypeMap.mustGetItem(newLodgingTypeId).name;

    return {
      sourceLodgingUnitName,
      sourceLodgingTypeName,
      newLodgingUnitName,
      newLodgingTypeName,
    };
  }, [lodgingUnitId, newLodgingUnitId, lodgingUnitMap, lodgingTypeMap]);

  const sameApptOtherCardPetNames = sameApptOtherCardPetList.map((pet) => pet.petName).join(', ');

  const handleClose = () => {
    onCancel();
    onClose();
  };

  const handleConfirm = useSerialCallback(async () => {
    if (dateConflict) {
      onCancel();
      onClose();
      return;
    }

    if (newLodgingUnitId) {
      reportData(ReportActionName.lodgingViewDragToChangeLodging);
    }

    if (newStartDate && newEndDate) {
      reportData(ReportActionName.lodgingViewDragToReschedule);
    }

    if (isEvaluation) {
      await dispatch(
        rescheduleEvaluationAppt({
          appointmentId,
          evaluationServiceSchedules: evaluationNewScheduleParams,
        }),
      );
    } else {
      const res = await dispatch(
        rescheduleLodgingAppt({
          isDaycareApptCard,
          appointmentId,
          serviceSchedules: hasSameDaycareWillChange ? multiDayNewScheduleParams : normalNewScheduleParams,
        }),
      );
      if (res.conflictServiceNames.length > 0) {
        setConflictInfo(res);
        return;
      }
    }
    if (dateIsChange) {
      dispatch(
        setTicketAlertsProps({
          ticketId: Number(appointmentId),
          customerId: Number(customerId),
          mode: AutoMessageType.AppointmentRescheduled,
        }),
      );
    }

    onClose();
  });

  const checkDropApptConflict = useSerialCallback(async () => {
    const finalLodgingUnitId = newLodgingUnitId ?? lodgingUnitId;
    const finalStartDate = newStartDate ?? startDate;
    const finalEndDate = newEndDate ?? endDate;
    const result = await dispatch(
      getLodgingOverCapacityInfo({
        appointmentId,
        lodgingUnitIds: [finalLodgingUnitId],
        startDate: stringToDateMessage(finalStartDate.format(DATE_FORMAT_EXCHANGE)),
        endDate: stringToDateMessage(finalEndDate.format(DATE_FORMAT_EXCHANGE)),
        lodgingUnitChange: { oldLodgingUnitId: lodgingUnitId, newLodgingUnitId: finalLodgingUnitId },
      }),
    );
    setCheckResult({ ...result });
  });

  const renderConfirmChangeContent = () => {
    const sourcePetNames = sourcePetNameList.join(', ');
    return (
      <div className="moe-flex moe-flex-col moe-gap-y-xs">
        <div className="moe-flex moe-items-center moe-flex-wrap moe-text-primary">
          <Text variant="regular" className="moe-mr-xxs">
            Are you sure to make the following changes for {sourcePetNames}?
          </Text>
          <Condition if={sameApptOtherCardPetNames}>
            <Text variant="regular">
              Dates for {sameApptOtherCardPetNames} from the same appointment will also be changed.
            </Text>
          </Condition>
          <Condition if={hasSameDaycareWillChange}>
            <Text variant="regular">Lodging assignment for daycare within the same stay will be changed together.</Text>
          </Condition>
        </div>

        <Condition if={newStartDate && newEndDate}>
          <div className="moe-flex moe-items-center moe-flex-wrap moe-text-primary">
            <Text variant="regular">Service date:</Text>
            <RescheduleLodgingApptDates
              startDate={startDate}
              endDate={endDate}
              startTime={startTime}
              endTime={endTime}
              className="moe-mx-xxs"
            />
            <Text variant="regular" className="moe-mx-xxs">
              to
            </Text>
            <RescheduleLodgingApptDates
              startDate={newStartDate}
              endDate={newEndDate}
              // boarding & darcare 只能跨天移动 ，且移动前后的startTime endTime应保持一致
              startTime={startTime}
              endTime={endTime}
            />
          </div>
        </Condition>

        <Condition if={newLodgingUnitId}>
          <div className="moe-flex moe-items-center moe-flex-wrap moe-text-primary">
            <Text variant="regular">Lodgings:</Text>
            <Heading size="5" className="moe-mx-xxs">
              {sourceLodgingUnitName}
              {sourceLodgingTypeName && `(${sourceLodgingTypeName})`}
            </Heading>
            <Text variant="regular" className="moe-mx-xxs">
              to
            </Text>
            <Heading size="5">
              {newLodgingUnitName}
              {newLodgingTypeName && `(${newLodgingTypeName})`}
            </Heading>
          </div>
        </Condition>
        <MedicationRescheduleAlert
          appointmentId={appointmentId}
          startDate={newStartDate?.format(DATE_FORMAT_EXCHANGE)}
          endDate={newEndDate?.format(DATE_FORMAT_EXCHANGE)}
        />
      </div>
    );
  };

  const renderConflictContent = () => {
    const associatedServiceNames = conflictServiceNames.join(', ');
    return (
      <Condition if={associatedServiceNames}>
        <Text variant="regular" className="moe-text-primary">
          There’s another associated service ({associatedServiceNames}) within this appointment scheduled outside of
          this new date range, please edit the associated service date first before making the date change to boarding.
        </Text>
      </Condition>
    );
  };

  useEffect(() => {
    if (visible) {
      setConflictInfo({ conflictServiceNames: [] });
      checkDropApptConflict();
      return () => {
        setCheckResult({
          lodgingOverCapacityCheckResult: { lodgingUnits: [], lodgingTypes: [] },
          businessClosedDateCheckResult: { closedDate: [] },
        });
      };
    }
  }, [visible]);

  return (
    <Modal
      size="s"
      isOpen={visible}
      title={title}
      showCancelButton={!dateConflict}
      confirmText={dateConflict ? 'OK' : 'Confirm'}
      onCancel={onCancel}
      onClose={handleClose}
      onConfirm={handleConfirm}
      autoCloseOnConfirm={false}
      confirmButtonProps={{ isLoading: checkDropApptConflict.isBusy() }}
    >
      <Spin className="moe-w-full moe-h-full" isLoading={checkDropApptConflict.isBusy()}>
        {(businessClosedDateCheckResult.closedDate.length > 0 ||
          lodgingOverCapacityCheckResult.lodgingUnits.length > 0) && (
          <ConflictCheckAlertDialogContent
            business={business}
            isShowEditText={false}
            className="moe-mb-8px-100"
            businessClosedDateCheckResult={businessClosedDateCheckResult}
            lodgingOverCapacityCheckResult={lodgingOverCapacityCheckResult}
            appointmentDateConflictCheckResult={{ conflictAppointments: [] }}
          />
        )}
        {dateConflict ? renderConflictContent() : renderConfirmChangeContent()}
      </Spin>
    </Modal>
  );
});
