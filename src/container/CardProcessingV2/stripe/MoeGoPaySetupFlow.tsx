import { OnboardStatus, OnboardVerificationStatus } from '@moego/api-web/moego/models/payment/v2/onboard_enums';
import { useSerialCallback } from '@moego/finance-utils';
import { FNK_PaymentVendorEnum, type useAccount } from '@moego/finance-web-kit';
import { Alert, AlertDialog, Button, Heading, Markup, Text, cn } from '@moego/ui';
import { useDispatch, useSelector } from 'amos';
import { Steps } from 'antd';
import React, { useRef } from 'react';
import ImageMoegoPaySetupScreenshot1Png from '../../../assets/image/moego-pay-setup-screenshot-1.png';
import ImageMoegoPaySetupScreenshot21Png from '../../../assets/image/moego-pay-setup-screenshot-2-1.png';
import ImageMoegoPaySetupScreenshot22Png from '../../../assets/image/moego-pay-setup-screenshot-2-2.png';
import ImageMoegoPaySetupScreenshotAdyen1Png from '../../../assets/image/moego-pay-setup-screenshot-adyen-1.png';
import ImageMoegoPaySetupScreenshotAdyen2Png from '../../../assets/image/moego-pay-setup-screenshot-adyen-2.png';
import { Condition } from '../../../components/Condition';
import { Case, Switch } from '../../../components/SwitchCase';
import { EnterMoeGoPaySettingSource, PATH_CREDIT_CARD_SETTING_MOEGO_PAY } from '../../../router/paths';
import { updateBusiness } from '../../../store/business/business.actions';
import { PreferPayTypes } from '../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../store/business/business.selectors';
import { useEnableFeature } from '../../../store/metadata/featureEnable.hooks';
import { META_DATA_KEY_LIST } from '../../../store/metadata/metadata.config';
import { createEnum } from '../../../store/utils/createEnum';
import { useRouteQueryV2 } from '../../../utils/RoutePath';
import { useBool } from '../../../utils/hooks/useBool';
import { FinanceActionName } from '../../../utils/reportData/finance';
import { reportData } from '../../../utils/tracker';
import { useCapitalBlocked } from '../../Finance/hooks/useCapitalBlocked';
import { useAdyenOnboardingStepOneModal } from '../components/AdyenOnboardingStepOneModal';
import { MoeGoPaySetupOnboarding } from './Capital/MoeGoPaySetupOnboarding';
import { MoeGoPaySetupFlowView, StepsView } from './MoeGoPaySetupFlow.styles';
import { SetupPage } from './SetupPage';

const { Step } = Steps;

const SetupSteps = createEnum({
  ProcessorSettings: [100, 'ProcessorSettings'],
  ConfirmFinished: [200, 'ConfirmFinished'],
});

const useShowSetupPage = (account: ReturnType<typeof useAccount>['value']) => {
  const query = useRouteQueryV2(PATH_CREDIT_CARD_SETTING_MOEGO_PAY);

  const fromOtherSource = [
    EnterMoeGoPaySettingSource.CapitalLanding,
    EnterMoeGoPaySettingSource.PaymentFLowIntegrate,
  ].includes(query.source || ('' as EnterMoeGoPaySettingSource));

  const isShow = useBool(!fromOtherSource && account?.onboardCurStep === 0);

  return {
    isShow: isShow.value,
    close: isShow.close,
  };
};

const useCapitalOnboard = () => {
  const query = useRouteQueryV2(PATH_CREDIT_CARD_SETTING_MOEGO_PAY);
  const isFromCapitalLanding = query.source === EnterMoeGoPaySettingSource.CapitalLanding;
  const { enable, toggle } = useEnableFeature(META_DATA_KEY_LIST.CapitalToMoeGoPayOnboardingEnable);
  const { value: isCapitalBlocked } = useCapitalBlocked();

  return {
    isShow: !isCapitalBlocked && isFromCapitalLanding && enable,
    close: () => toggle(false),
  };
};

interface Props {
  inFullMode: boolean;
  onFinish: () => void;
  account: ReturnType<typeof useAccount>['value'];
  refreshAccount: () => Promise<void>;
}

/**
 * The Setup flow page of MoeGo Pay. Must be rendered after "getStripeAccount" and "getStripeIdentityStatus" is loaded.
 * @param inFullMode Whether the flow is displayed in full mode (without tab bar).
 * @param onFinish
 */
export const MoeGoPaySetupFlow = (props: Props) => {
  const { inFullMode, onFinish, account, refreshAccount } = props;
  const dispatch = useDispatch();
  const [business] = useSelector(selectCurrentBusiness());
  const confirmAdyenStepOne = useAdyenOnboardingStepOneModal();
  // The user has set primary pay type to square before, need extra step to auto-set to MoeGo Pay.
  const needPrimarySettingsStep = useRef(business.primaryPayType === PreferPayTypes.Square).current;
  const isPrimarySaved = useBool();
  const isFinishing = useBool();
  const isConfirmedFirstStep = useBool();
  const query = useRouteQueryV2(PATH_CREDIT_CARD_SETTING_MOEGO_PAY);
  const { isShow: isShowCapitalOnboarding, close: closeShowCapitalOnboarding } = useCapitalOnboard();
  const { isShow: isShowSetupPage, close: closeShowSetupPage } = useShowSetupPage(account);
  // 这个是仅代表 vendor 那边的 onboarding 是否结束，而不是 MGP onboarding 是否结束，MGP 这边还有一个 confirm finished 的步骤。
  const isFinishedVendorOnboarding = account?.onboardStatus === OnboardStatus.ONBOARDED;
  const curVendorOnboardingStep = account?.onboardCurStep ?? 0;

  const getStep = () => {
    if (!isFinishedVendorOnboarding) {
      return curVendorOnboardingStep;
    }
    if (needPrimarySettingsStep && !isPrimarySaved.value) {
      return SetupSteps.ProcessorSettings;
    }
    return SetupSteps.ConfirmFinished;
  };

  const currentStep = getStep();

  const handleAction = (action: FinanceActionName, payload?: object) => {
    try {
      closeShowCapitalOnboarding();
      reportData(action, { source: query.source || 'unknown', primary_type: business.primaryPayType, ...payload });
    } catch (e) {
      console.error(e);
    }
  };

  const finishSetup = async () => {
    handleAction(FinanceActionName.MoeGoPaySettingFinishedClick);

    isFinishing.as(true);
    try {
      await onFinish();
    } catch (e) {
      console.error(e);
    }
    isFinishing.as(false);
  };

  const handleVerifyVendorOnboardingStep = useSerialCallback(async (step: number) => {
    if (account?.vendor === FNK_PaymentVendorEnum.Stripe) {
      if (step === 0) {
        handleAction(FinanceActionName.MoeGoPaySettingIdVerifyClick);
        const link = await account.getStripeIDVerificationLink({ returnUrl: location.href });
        window.open(link, '_self');
      }
      if (step === 1) {
        handleAction(FinanceActionName.MoeGoPaySettingBizVerifyClick);
        const url = (await account.getOnboardingLink({ returnUrl: location.href })).url;
        window.open(url, '_self');
      }
    }

    if (account?.vendor === FNK_PaymentVendorEnum.Adyen) {
      if (step === 0) {
        await confirmAdyenStepOne({
          onConfirm: async (data) => {
            await account?.submitAdyenPreConfig({
              phoneNumber: business.phoneNumber,
              businessConfig: {},
              ...data,
            });
            await refreshAccount();
          },
        });
        isConfirmedFirstStep.open();
      }

      if (step === 1) {
        if (!account) return;
        const url = (await account.getOnboardingLink({ returnUrl: location.href })).url;
        window.open(url, '_self');
      }
    }
  });

  const handleCompleteSettingClick = useSerialCallback(async () => {
    await dispatch(updateBusiness({ primaryPayType: PreferPayTypes.Stripe }));
    isPrimarySaved.as(true);
    handleAction(FinanceActionName.MoeGoPaySettingSquareToStripe, { primary_type: PreferPayTypes.Stripe });
  });

  const handleSetupPrimaryLater = () => {
    AlertDialog.open({
      title: <Heading size="6">Features for MoeGo Pay will be turned off, including:</Heading>,
      content: (
        <div className="moe-ml-m">
          <ul className="moe-list-disc moe-text-tertiary">
            <li>Processing Fee by Client</li>
            <li>Smart Tip (Tip rate and tip screen)</li>
            <li>Prepayment / Deposit (online booking)</li>
          </ul>
          <Text variant="regular">Are you sure to proceed?</Text>
        </div>
      ),
      confirmText: 'Set to MoeGo Pay ',
      cancelText: 'Keep Square',
      onConfirm: handleCompleteSettingClick,
      onCancel: finishSetup,
    });
    // modalApi.confirm({
    //   title: (
    //     <span style={{ fontSize: '14px', lineHeight: '18px' }}>
    //       Features for MoeGo Pay will be turned off, including:
    //     </span>
    //   ),
    //   content: (
    //     <div>
    //       <ul style={{ paddingInlineStart: '15px', color: '#666' }}>
    //         <li>Processing Fee by Client</li>
    //         <li>Smart Tip (Tip rate and tip screen)</li>
    //         <li>Prepayment / Deposit (online booking)</li>
    //       </ul>
    //       Are you sure to proceed?
    //     </div>
    //   ),
    //   okText: 'Set to MoeGo Pay ',
    //   cancelText: 'Keep Square',
    //   width: '480px',
    //   onOk: handleCompleteSettingClick,
    //   // Directly finish setup page
    //   onCancel: finishSetup,
    // });
  };

  const getVendorOnboardingConfig = () => {
    if (account?.vendor === FNK_PaymentVendorEnum.Stripe) {
      const getFirstStepDescription = () => {
        if (currentStep > 0) {
          return (
            <Text variant="small" className="moe-text-tertiary">
              Congratulations, one more step to go!
            </Text>
          );
        }
        return (
          <>
            <ul className={'moe-mb-0 moe-pl-[20px] moe-list-disc'}>
              <li>
                <Text variant="small">
                  For this step, have a government issued{' '}
                  <Markup variant="small" className="moe-inline">
                    {`ID (Driver's license, passport or other IDs)`}
                  </Markup>{' '}
                  ready.
                </Text>
              </li>
              <li>
                <Text variant="small">Click the button below to finish the verification process</Text>
              </li>
            </ul>
            <Button
              className="moe-mt-[16px]"
              onPress={() => handleVerifyVendorOnboardingStep(0)}
              isLoading={handleVerifyVendorOnboardingStep.isBusy()}
            >
              Verify
            </Button>
          </>
        );
      };

      const getSecondStepDescription = () => {
        const verificationInfo = account.getVerificationInfo(1);
        const status = verificationInfo?.status;
        const isPending = status === OnboardVerificationStatus.PENDING;
        const isRejectedOrInvalid =
          status === OnboardVerificationStatus.INVALID || status === OnboardVerificationStatus.REJECTED;
        const message = verificationInfo?.message;

        if (currentStep > 1) {
          return (
            <Text variant="small" className="moe-text-tertiary">
              Woohoo! All done!
            </Text>
          );
        }

        return (
          <>
            <ul className={'moe-mb-0 moe-pl-[20px] moe-list-disc'}>
              <li>
                <Text variant="small">
                  For this step, have your{' '}
                  <Markup variant="small" className="moe-inline">
                    SSN, EIN (Employer Identification Number)
                  </Markup>{' '}
                  documentation and your{' '}
                  <Markup variant="small" className="moe-inline">
                    payout bank account information
                  </Markup>{' '}
                  ready.
                </Text>
              </li>
              <Condition if={currentStep === 1}>
                <li>
                  <Text variant="small">Click the button below to finish the verification process</Text>
                </li>
              </Condition>
            </ul>
            <Condition if={!isConfirmedFirstStep.value}>
              <Switch shortCircuit>
                <Case if={isPending}>
                  <Alert color="warning" isCloseable={false} isBordered className="moe-mt-s">
                    Please wait a few minutes while we verify your business information. You can check back later or
                    refresh this page to see the result.
                  </Alert>
                </Case>
                <Case if={isRejectedOrInvalid}>
                  <Alert color="warning" isCloseable={false} isBordered className="moe-mt-s">
                    {message}
                  </Alert>
                </Case>
              </Switch>
            </Condition>
            <Condition if={currentStep === 1}>
              <Button
                className="moe-mt-[16px]"
                onPress={() => handleVerifyVendorOnboardingStep(1)}
                isLoading={handleVerifyVendorOnboardingStep.isBusy()}
              >
                Verify
              </Button>
            </Condition>
          </>
        );
      };

      return [
        {
          title: 'Identity verification',
          description: getFirstStepDescription(),
        },
        {
          title: 'Business verification',
          description: getSecondStepDescription(),
        },
      ];
    }

    if (account?.vendor === FNK_PaymentVendorEnum.Adyen) {
      const getFirstStepDescription = () => {
        if (currentStep > 0) {
          return (
            <Text variant="small" className="moe-text-tertiary">
              Congratulations, one more step to go!
            </Text>
          );
        }

        // TODO（Sam）：换文案
        return (
          <>
            <ul className={'moe-mb-0 moe-pl-[20px] moe-list-disc'}>
              <li>
                <Text variant="small">
                  Select your business type (sole proprietorship, company, partnership or association).
                </Text>
              </li>
              <li>
                <Text variant="small">Provide required details: name, address, and governing country.</Text>
              </li>
            </ul>
            <Button
              className="moe-mt-s"
              onPress={() => handleVerifyVendorOnboardingStep(0)}
              isLoading={handleVerifyVendorOnboardingStep.isBusy()}
            >
              Start
            </Button>
          </>
        );
      };

      const getSecondStepDescription = () => {
        const verificationInfo = account?.getVerificationInfo(1);
        const status = verificationInfo?.status;
        const isPending = status === OnboardVerificationStatus.PENDING;
        const isRejectedOrInvalid =
          status === OnboardVerificationStatus.INVALID || status === OnboardVerificationStatus.REJECTED;
        const message = verificationInfo?.message;

        if (currentStep > 1) {
          return (
            <Text variant="small" className="moe-text-tertiary">
              Woohoo! All done!
            </Text>
          );
        }

        return (
          <>
            <ul className={'moe-mb-0 moe-pl-[20px] moe-list-disc'}>
              <li>
                <Text variant="small">
                  For this step, have your{' '}
                  <Markup variant="small" className="moe-inline">
                    SSN, EIN (Employer Identification Number)
                  </Markup>{' '}
                  documentation and your{' '}
                  <Markup variant="small" className="moe-inline">
                    payout bank account information
                  </Markup>{' '}
                  ready.
                </Text>
              </li>
              <Condition if={currentStep === 1}>
                <li>
                  <Text variant="small">Click the button below to finish the verification process.</Text>
                </li>
              </Condition>
            </ul>
            <Condition if={!isConfirmedFirstStep.value}>
              <Switch shortCircuit>
                <Case if={isPending}>
                  <Alert color="warning" isCloseable={false} isBordered className="moe-mt-s">
                    Please wait a few minutes while we verify your business information. You can check back later or
                    refresh this page to see the result.
                  </Alert>
                </Case>
                <Case if={isRejectedOrInvalid}>
                  <Alert color="warning" isCloseable={false} isBordered className="moe-mt-s">
                    {message}
                  </Alert>
                </Case>
              </Switch>
            </Condition>
            <Condition if={currentStep === 1}>
              <Button
                className="moe-mt-[16px]"
                onPress={() => handleVerifyVendorOnboardingStep(1)}
                isLoading={handleVerifyVendorOnboardingStep.isBusy()}
              >
                Verify
              </Button>
            </Condition>
          </>
        );
      };

      return [
        {
          title: 'Entity setup',
          description: getFirstStepDescription(),
        },
        {
          title: 'Business verification',
          description: getSecondStepDescription(),
        },
      ];
    }

    return [];
  };
  const stepConfig = getVendorOnboardingConfig();

  const renderVendorOnboardingSteps = () => {
    return stepConfig.map((step, index) => <Step key={index} title={step.title} description={step.description} />);
  };

  const renderPrimarySettingsDesc = () => {
    if (currentStep < SetupSteps.ProcessorSettings) {
      return null;
    }
    if (currentStep > SetupSteps.ProcessorSettings) {
      const message =
        business.primaryPayType === PreferPayTypes.Stripe
          ? 'MoeGo Pay is set as the primary card processor.'
          : 'MoeGo Pay is not set as the primary card processor. Please toggle on to start using MoeGo Pay.';
      return (
        <Text variant="small" className="moe-text-tertiary">
          {message}
        </Text>
      );
    }

    return (
      <>
        <div className="moe-mt-s moe-flex moe-items-center moe-gap-x-s">
          <Button onPress={handleCompleteSettingClick} isDisabled={handleCompleteSettingClick.isBusy()}>
            Complete setup
          </Button>
          <div className="moe-cursor-pointer moe-text-tertiary" onClick={handleSetupPrimaryLater}>
            Set up later
          </div>
        </div>
      </>
    );
  };

  const renderOnboardingImage = () => {
    if (isFinishedVendorOnboarding) return;

    if (account?.vendor === FNK_PaymentVendorEnum.Stripe) {
      return (
        <div className="moe-flex moe-flex-col">
          <Switch>
            <Switch.Case if={account.onboardCurStep === 0}>
              <img
                className="moe-mt-[232px] moe-ml-[14px] moe-w-[269.5px] moe-h-[213px]"
                src={ImageMoegoPaySetupScreenshot1Png}
              />
            </Switch.Case>
            <Switch.Case if={account.onboardCurStep === 1}>
              <>
                <img
                  className="moe-mt-[213px] moe-ml-[11px] moe-w-[274.5px] moe-h-[129px]"
                  src={ImageMoegoPaySetupScreenshot21Png}
                />
                <img
                  className="moe-mt-[-6px] moe-ml-[14px] moe-w-[269.5px] moe-h-[205px]"
                  src={ImageMoegoPaySetupScreenshot22Png}
                />
              </>
            </Switch.Case>
          </Switch>
        </div>
      );
    }

    if (account?.vendor === FNK_PaymentVendorEnum.Adyen) {
      // TODO（Sam）：新图片。
      return (
        <div className="moe-flex moe-flex-col">
          <Switch>
            <Switch.Case if={account?.onboardCurStep === 0}>
              <img
                className="moe-mt-[232px] moe-ml-[14px] moe-w-[402px] moe-h-[308px]"
                src={ImageMoegoPaySetupScreenshotAdyen1Png}
              />
            </Switch.Case>
            <Switch.Case if={account?.onboardCurStep === 1}>
              <img
                className="moe-mt-[232px] moe-ml-[14px] moe-w-[395px] moe-h-[280px]"
                src={ImageMoegoPaySetupScreenshotAdyen2Png}
              />
            </Switch.Case>
          </Switch>
        </div>
      );
    }

    return null;
  };

  if (isShowSetupPage) {
    return (
      <SetupPage
        handleSetupMoeGoPay={closeShowSetupPage}
        className={cn({
          'moe-mt-0': inFullMode,
          'moe-mt-m': !inFullMode,
        })}
      />
    );
  }

  return (
    <MoeGoPaySetupFlowView className={cn({ 'full-mode': inFullMode })}>
      <StepsView>
        <MoeGoPaySetupOnboarding visible={isShowCapitalOnboarding} onClose={closeShowCapitalOnboarding}>
          <div className={'steps-view-inner moe-relative'}>
            <Heading size="2">
              {currentStep === SetupSteps.ConfirmFinished ? 'Congratulations!' : 'Set up your MoeGo Pay account'}
            </Heading>
            <Text variant="small" className="moe-text-tertiary moe-mt-s">
              {currentStep !== SetupSteps.ConfirmFinished ? (
                <>
                  Follow the steps below to set up your MoeGo Pay account. And before you start, please make sure you
                  have the relevant documentation with you.{' '}
                  {/* <a href={URL_MOEGO_PAY_WIKI} target={'_blank'}>
                    View wiki guide
                  </a> */}
                </>
              ) : (
                <>Enjoy the exclusive features and start earning more.</>
              )}
            </Text>

            <Steps
              className={'steps'}
              style={{ marginTop: 40 }}
              current={currentStep}
              direction={'vertical'}
              size={'small'}
            >
              {renderVendorOnboardingSteps()}

              {needPrimarySettingsStep && (
                <Step title={'Set primary card processor to MoeGo Pay'} description={renderPrimarySettingsDesc()} />
              )}
            </Steps>
            {currentStep === SetupSteps.ConfirmFinished && (
              <Button className="moe-mt-[16px]" isLoading={isFinishing.value} onPress={finishSetup}>
                {`Let's go`}
              </Button>
            )}
          </div>
        </MoeGoPaySetupOnboarding>
      </StepsView>
      {renderOnboardingImage()}
    </MoeGoPaySetupFlowView>
  );
};
