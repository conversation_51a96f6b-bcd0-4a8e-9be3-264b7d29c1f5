import { selector } from 'amos';
import dayjs, { type Dayjs } from 'dayjs';
import { currentBusinessIdBox } from '../business/business.boxes';
import { selectAllBusinessClosedDates } from '../business/closedDate.selectors';
import { selectHolidayList } from '../business/holiday.selectors';
import { type FullWeekDay, FullWeekDayList } from '../onlineBooking/models/OnlineBookingPreference';
import {
  bizScheduleMapBox,
  calendarViewDataMapBox,
  CalendarViewDataRecord,
  calendarViewSlotDataMapBox,
  CalendarViewSlotDataRecord,
  editingWorkingHourStaffIdBox,
  staffScheduleCalendarViewList,
  staffScheduleDateOverrideMapBox,
  staffScheduleServiceAreaMapBox,
  staffScheduleSlotOverrideMapBox,
  staffScheduleWorkingHourMapBox,
  staffScheduleWorkingSlotMapBox,
} from './staffSchedule.boxes';
import { AvailabilityType, DateOverrideType, type WeekTimeValue, type WeekSlotValue } from './staffSchedule.types';
import { type EnumValues } from '../utils/createEnum';
import { getRotatingWeekMeta } from '../../container/settings/Settings/StaffSetting/ShiftManagement/utils/rotatingWeek';
import { merge } from 'lodash';
import { getDefaultLimit } from '../../container/settings/Settings/StaffSetting/ShiftManagement/components/Limitation/Limitation.type';

export const selectStaffSchedule = selector(
  (
    select,
    staffId: number = select(editingWorkingHourStaffIdBox),
    availabilityType: EnumValues<typeof AvailabilityType>,
  ) => {
    return availabilityType === AvailabilityType.BY_TIME
      ? select(staffScheduleWorkingHourMapBox.mustGetItem(staffId))
      : select(staffScheduleWorkingSlotMapBox.mustGetItem(staffId));
  },
);

export const selectCalendarViewData = selector(
  (select, staffId: number, availabilityType: EnumValues<typeof AvailabilityType>) => {
    const businessId = select(currentBusinessIdBox);

    const isByTime = availabilityType === AvailabilityType.BY_TIME;
    return isByTime
      ? select(calendarViewDataMapBox.mustGetItem(CalendarViewDataRecord.ownKey(businessId, staffId)))
      : select(calendarViewSlotDataMapBox.mustGetItem(CalendarViewSlotDataRecord.ownKey(businessId, staffId)));
  },
);

export const selectStaffScheduleByTime = selector((select, staffId: number = select(editingWorkingHourStaffIdBox)) => {
  return select(staffScheduleWorkingHourMapBox.mustGetItem(staffId));
});

export const selectStaffScheduleServiceArea = selector(
  (select, staffId: number = select(editingWorkingHourStaffIdBox)) => {
    return select(staffScheduleServiceAreaMapBox.mustGetItem(staffId));
  },
);

export const selectBizSchedule = selector((select, businessId = select(currentBusinessIdBox)) => {
  return select(bizScheduleMapBox.mustGetItem(businessId)).timeData;
});

export const selectStaffScheduleOverride = selector((select, staffId: number) => {
  return select(staffScheduleDateOverrideMapBox.mustGetItem(staffId));
});

export const selectStaffScheduleSlotOverride = selector((select, staffId: number) => {
  return select(staffScheduleSlotOverrideMapBox.mustGetItem(staffId));
});

export const selectStaffAllOverrideDates = selector((select, staffId: number) => {
  const { ongoing = [], history = [] } = select(selectStaffScheduleOverride(staffId));
  return ongoing
    .map((i) => ({ ...i, overrideType: DateOverrideType.Ongoing }))
    .concat(history.map((i) => ({ ...i, overrideType: DateOverrideType.History })));
});

export const selectStaffAllScheduleSlotOverride = selector((select, staffId: number) => {
  const { ongoing, history } = select(selectStaffScheduleSlotOverride(staffId));
  return ongoing
    .map((i) => ({ ...i, overrideType: DateOverrideType.Ongoing }))
    .concat(history.map((i) => ({ ...i, overrideType: DateOverrideType.History })));
});

export const selectStaffOnGoingOverrideDates = selector((select, staffId: number) => {
  const { ongoing } = select(staffScheduleDateOverrideMapBox.mustGetItem(staffId));
  return ongoing;
});

export const selectStaffWeekSlotRangeAt = selector((select, staffId: number, date: Dayjs) => {
  const currentBusinessId = select(currentBusinessIdBox);
  const customBizClosedDates = select(selectAllBusinessClosedDates(currentBusinessId));
  const holidays = select(selectHolidayList(currentBusinessId));
  const weekStart = date.startOf('week');
  const weekEnd = date.endOf('week');
  const closedDates = customBizClosedDates.concat(holidays).map((i) => [dayjs(i.startDate), dayjs(i.endDate)]);
  const bizClosedWeeks = FullWeekDayList.reduce(
    (pre, cur, index) => {
      const curDate = weekStart.add(index, 'day');
      const isMatch = closedDates.some((dates) => dates[0] <= curDate && dates[1] >= curDate);
      if (isMatch) {
        const dayKey = cur.toLowerCase();
        return {
          ...pre,
          [dayKey]: {
            isAvailable: false,
          },
        };
      }
      return pre;
    },
    {} as Partial<WeekSlotValue>,
  );

  const staffSchedule = select(staffScheduleWorkingSlotMapBox.mustGetItem(staffId));
  const list = select(selectStaffAllScheduleSlotOverride(staffId));

  // 和 by time 不一样， by slot 的 override 需要覆盖 slotDailySetting、slotList 的值
  const overrides = list.filter((i) => {
    const cur = dayjs(i.overrideDate);
    return cur >= weekStart && cur <= weekEnd;
  });

  const overrideWeeks = overrides.reduce((pre, i) => {
    const key = dayjs(i.overrideDate).format('dddd').toLowerCase();
    return { ...pre, [key]: i.value };
  }, {} as WeekSlotValue);

  const { weekKey } = getRotatingWeekMeta({
    date,
    startDate: staffSchedule.startDate,
    endDate: staffSchedule.endDate,
    scheduleType: staffSchedule.scheduleType,
  });
  const weekSlotData = staffSchedule[weekKey];

  return merge({ ...weekSlotData, ...overrideWeeks }, bizClosedWeeks);
});

/**
/**
 * 根据当前日期获取当周数据，合并 closed dates 和 holidays，考虑 cycle week
 */
export const selectStaffWeekTimeAt = selector((select, staffId: number, date: Dayjs) => {
  const currentBusinessId = select(currentBusinessIdBox);
  const bizSchedule = select(selectBizSchedule());
  const customBizClosedDates = select(selectAllBusinessClosedDates(currentBusinessId));
  const holidays = select(selectHolidayList(currentBusinessId));
  const weekStart = date.startOf('week');
  const weekEnd = date.endOf('week');
  const closedDates = customBizClosedDates.concat(holidays).map((i) => [dayjs(i.startDate), dayjs(i.endDate)]);
  const bizClosedWeeks = FullWeekDayList.reduce(
    (pre, cur, index) => {
      const curDate = weekStart.add(index, 'day');
      const isMatch = closedDates.some((dates) => dates[0] <= curDate && dates[1] >= curDate);
      if (isMatch) {
        return {
          ...pre,
          [cur.toLowerCase()]: {
            isAvailable: false,
            timeRange: [],
            limit: getDefaultLimit(),
          },
        };
      }
      return pre;
    },
    {} as Partial<WeekTimeValue>,
  );

  const bizScheduleWeeks = FullWeekDayList.reduce(
    (map, key) => {
      const lowerCaseKey = key.toLowerCase() as Lowercase<FullWeekDay>;
      const timeRange = bizSchedule[lowerCaseKey];
      map[lowerCaseKey] = {
        isAvailable: timeRange.length > 0,
        timeRange,
        limit: getDefaultLimit(),
      };
      return map;
    },
    {} as Partial<WeekTimeValue>,
  );

  const staffSchedule = select(staffScheduleWorkingHourMapBox.mustGetItem(staffId));
  const overrideList = select(selectStaffAllOverrideDates(staffId));
  const overrides = overrideList.filter((i) => {
    const cur = dayjs(i.overrideDate);
    return cur >= weekStart && cur <= weekEnd;
  });
  const overrideWeeks = overrides.reduce((pre, i) => {
    const key = dayjs(i.overrideDate).format('dddd').toLowerCase();
    return { ...pre, [key]: i.value };
  }, {} as WeekTimeValue);

  if (staffSchedule.isWithInCycleWeek(date)) {
    const { weekKey } = getRotatingWeekMeta({
      date,
      startDate: staffSchedule.startDate,
      endDate: staffSchedule.endDate,
      scheduleType: staffSchedule.scheduleType,
    });
    const weekTimeData = staffSchedule[weekKey];
    return { ...weekTimeData, ...overrideWeeks, ...bizClosedWeeks };
  }

  return {
    ...bizScheduleWeeks,
    ...overrideWeeks,
    ...bizClosedWeeks,
  };
});

/**
 * 当前生效的weeks，不考虑closed dates 和 holidays
 */
export const selectStaffRegularWeekTimeRangeAt = selector((select, staffId: number, date: Dayjs) => {
  const staffSchedule = select(staffScheduleWorkingHourMapBox.mustGetItem(staffId));
  const bizSchedule = select(selectBizSchedule());

  if (staffSchedule.isWithInCycleWeek(date)) {
    const { weekKey } = getRotatingWeekMeta({
      date,
      startDate: staffSchedule.startDate,
      endDate: staffSchedule.endDate,
      scheduleType: staffSchedule.scheduleType,
    });
    const weekTimeData = staffSchedule[weekKey];
    return { ...weekTimeData };
  }

  return { ...bizSchedule };
});

export const selectCalendarViewType = selector((select) => {
  const businessId = select(currentBusinessIdBox);
  return select(staffScheduleCalendarViewList).getList(businessId);
});
