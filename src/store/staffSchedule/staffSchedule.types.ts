import dayjs, { type Dayjs } from 'dayjs';
import { type FullWeekDay, FullWeekDayList } from '../onlineBooking/models/OnlineBookingPreference';
import {
  type WorkingHourValue,
  type ServiceAreaRange,
  type WorkingHourTimeRange,
  type WorkingSlotValue,
} from '../staff/staff.boxes';
import { createEnum } from '../utils/createEnum';
import { OneDayMinutes } from '../autoMessage/autoReply.boxes';

export enum RotateWeek {
  firstWeek = 'firstWeek',
  secondWeek = 'secondWeek',
  thirdWeek = 'thirdWeek',
  forthWeek = 'forthWeek',
}

export type RotateWeekKeyType = keyof typeof RotateWeek;

export const RotateWeekList = [RotateWeek.firstWeek, RotateWeek.secondWeek, RotateWeek.thirdWeek, RotateWeek.forthWeek];

export const StaffWorkingHourViewType = createEnum({
  ListView: [1, 'List view'],
  CalendarView: [2, 'Calendar view'],
});

export enum WeekKeyMapNum {
  firstWeek = 1,
  secondWeek = 2,
  thirdWeek = 3,
  forthWeek = 4,
}

export const AvailabilityType = createEnum({
  BY_TIME: [1, 'By time'],
  BY_SLOT: [2, 'By slot'],
});

export const StaffScheduleType = createEnum({
  Every1Week: [WeekKeyMapNum.firstWeek, 'Every week'],
  Every2Week: [WeekKeyMapNum.secondWeek, 'Every 2 weeks'],
  Every3Week: [WeekKeyMapNum.thirdWeek, 'Every 3 weeks'],
  Every4Week: [WeekKeyMapNum.forthWeek, 'Every 4 weeks'],
});

export enum DateOverrideType {
  Ongoing,
  History,
}

export enum ClosedDateType {
  Custom = 'custom',
  Holiday = 'holiday',
}

export const StaffWorkingHourType = createEnum({
  Regular: [1, { label: 'Regular', color: '#FFA46B' }],
  Overrides: [2, { label: 'Overrides', color: '#FDC4A3' }],
});

export type FullWeekData<T> = {
  [Property in Lowercase<FullWeekDay>]: T;
};
export type WeekTimeValue = FullWeekData<WorkingHourValue>;
export type WeekSlotValue = FullWeekData<WorkingSlotValue>;
export type WeekServiceAreaValue = FullWeekData<ServiceAreaRange[]>;

export type WeekTimeValueLegacy = FullWeekData<WorkingHourTimeRange[]>;

export const getDefaultWeekTimeValueLegacy = (): WeekTimeValueLegacy =>
  FullWeekDayList.reduce((pre, week) => ({ ...pre, [week.toLowerCase()]: [] }), {} as WeekTimeValueLegacy);

export const getDefaultWeekTimeValue = (): WeekTimeValue =>
  FullWeekDayList.reduce(
    (pre, week) => ({
      ...pre,
      [week.toLowerCase()]: {
        isAvailable: true,
        timeRange: [],
        limit: {
          petSizeLimits: [],
          petBreedLimits: [],
          serviceLimits: [],
        },
      },
    }),
    {} as WeekTimeValue,
  );

export const getDefaultWeekSlotValue = (): WeekSlotValue => {
  const weekSlotValue = FullWeekDayList.reduce(
    (pre, week) => ({
      ...pre,
      [week.toLowerCase()]: {
        slotDailySetting: {
          startTime: 0,
          endTime: OneDayMinutes - 5,
          capacity: 1,
          isAvailable: true,
          limit: {
            petSizeLimits: [],
            petBreedLimits: [],
            serviceLimits: [],
          },
        },
        slotHourSettingList: [
          {
            startTime: 0,
            capacity: 1,
            petSizeLimits: [],
            petBreedLimits: [],
            serviceLimits: [],
          },
        ],
      },
    }),
    {} as WeekSlotValue,
  );

  return weekSlotValue;
};

export const getDefaultServiceAreaValue = (): WeekServiceAreaValue =>
  FullWeekDayList.reduce((pre, week) => ({ ...pre, [week.toLowerCase()]: [] }), {} as WeekServiceAreaValue);

// 相当于 Ends = never
export const MaxStaffEndDate = dayjs('9999-12-31');

// 相当于 Start = 1970-01-01;
export const MinStaffStartDate = dayjs(new Date(0)).startOf('day');

export const isMaxStaffEndDate = (d: Dayjs | null) => (d ? d.isSame(MaxStaffEndDate, 'day') : false);

export type StaffScheduleDraftData<T extends StaffScheduleBase> = T & {
  origin: Omit<T, 'staffId' | 'endDate'> | null;
};

export interface StaffScheduleBase<T extends FullWeekData<unknown> = FullWeekData<unknown>> {
  staffId: number;
  scheduleType: number;
  /** 当前staff选中的week1/week2 */
  // activeScheduleType: string;
  startDate: Dayjs;
  endDate: Dayjs;
  firstWeek: T;
  secondWeek: T;
  thirdWeek: T;
  forthWeek: T;
}

export type StaffScheduleWorkingHourLegacy = StaffScheduleBase<WeekTimeValueLegacy>;
export type StaffScheduleWorkingHour = StaffScheduleBase<WeekTimeValue>;
export type StaffScheduleWorkingSlot = StaffScheduleBase<WeekSlotValue>;
export type StaffScheduleServiceArea = StaffScheduleBase<WeekServiceAreaValue>;

export interface BizScheduleWorkingHour {
  businessId: number;
  timeData: WeekTimeValueLegacy;
}

export type WeekKeyTypes = keyof Pick<StaffScheduleBase, 'firstWeek' | 'secondWeek' | 'thirdWeek' | 'forthWeek'>;

export interface StaffScheduleDateOverride<T = string> {
  overrideDate: T;
  value: WorkingHourValue;
}

export interface StaffScheduleSlotOverride<T = string> {
  overrideDate: T;
  value: WorkingSlotValue;
}

export interface StaffScheduleAreaOverride<T = string> {
  id: number;
  overrideDate: T;
  workingArea: ServiceAreaRange[];
}

export interface DateOverrideRecord<T = StaffScheduleDateOverride | StaffScheduleSlotOverride> {
  staffId: number;
  history: T[];
  ongoing: T[];
}

export interface AreaOverrideRecord {
  staffId: number;
  history: StaffScheduleAreaOverride[];
  ongoing: StaffScheduleAreaOverride[];
}

export interface DateOverrideDrawerValue {
  dates?: Dayjs[];
  value: WorkingHourValue;
  workingArea?: ServiceAreaRange[];
}

export interface DateOverrideDrawerSlotValue {
  dates?: Dayjs[];
  value?: WorkingSlotValue;
}
