/* eslint-disable sonarjs/no-identical-functions */
import dayjs, { type Dayjs } from 'dayjs';
import { Record } from 'immutable';
import { type OpenApiModels } from '../../openApi/schema';
import { OwnList, createOwnListBox } from '../utils/OwnList';
import { createRecordMapBox } from '../utils/RecordMap';
import { ID_ANONYMOUS, isNormal } from '../utils/identifier';
import { createBox } from '../utils/utils';
import {
  type AreaOverrideRecord,
  AvailabilityType,
  type BizScheduleWorkingHour,
  type DateOverrideRecord,
  MaxStaffEndDate,
  type StaffScheduleDateOverride,
  type StaffScheduleDraftData,
  type StaffScheduleServiceArea,
  type StaffScheduleSlotOverride,
  StaffScheduleType,
  type StaffScheduleWorkingHour,
  type StaffScheduleWorkingSlot,
  StaffWorkingHourViewType,
  WeekKeyMapNum,
  type WeekKeyTypes,
  getDefaultServiceAreaValue,
  getDefaultWeekSlotValue,
  getDefaultWeekTimeValue,
  getDefaultWeekTimeValueLegacy,
} from './staffSchedule.types';
import { type WorkingHourValue, type WorkingSlotValue } from '../staff/staff.boxes';
import { cloneDeep, isEqual, omit } from 'lodash';

export const editingWorkingHourStaffIdBox = createBox('staffSchedule/current/staff', ID_ANONYMOUS);

export const staffScheduleViewTypeBox = createBox(
  'staffSchedule/current/view/type',
  StaffWorkingHourViewType.CalendarView,
);

export const staffScheduleAvailabilityTypeBox = createBox(
  'staffSchedule/current/availability/type',
  AvailabilityType.BY_TIME,
);

export type CalendarViewListType = 'serviceArea' | 'workingHour';
export const staffScheduleCalendarViewList = createOwnListBox(
  'staffSchedule/calendar/view',
  new OwnList<number, CalendarViewListType, string>(0),
);

export const currentWorkingHourWeekBox = createBox('staffSchedule/current/week', dayjs().startOf('week'));

export const isWithInCycleWeek = (date: Dayjs, startDate: Dayjs, endDate: Dayjs, scheduleType: WeekKeyMapNum) => {
  if (scheduleType === StaffScheduleType.Every1Week) {
    return true;
  }

  return date >= startDate && date <= endDate;
};

export class StaffScheduleWorkingHourRecord extends Record<StaffScheduleDraftData<StaffScheduleWorkingHour>>({
  staffId: ID_ANONYMOUS,
  scheduleType: StaffScheduleType.Every1Week,
  firstWeek: getDefaultWeekTimeValue(),
  secondWeek: getDefaultWeekTimeValue(),
  thirdWeek: getDefaultWeekTimeValue(),
  forthWeek: getDefaultWeekTimeValue(),
  startDate: dayjs().startOf('date'),
  endDate: MaxStaffEndDate,
  origin: null,
}) {
  /** staff 数据是否加载 */
  get isValidStaff() {
    return isNormal(this.staffId);
  }

  getWeekData(weekNum: WeekKeyMapNum) {
    const weekType = WeekKeyMapNum[Number(weekNum)] as WeekKeyTypes;
    return this[weekType];
  }

  /** 是否在循环的week时间段内 */
  isWithInCycleWeek(date = dayjs()) {
    const { startDate, endDate, scheduleType } = this;
    return isWithInCycleWeek(date, startDate, endDate, scheduleType);
  }

  isDirty() {
    if (!this.origin) return false;

    const { scheduleType, firstWeek, secondWeek, thirdWeek, forthWeek } = this.toObject();
    const draftData = { scheduleType, firstWeek, secondWeek, thirdWeek, forthWeek };
    const originData = omit(this.origin, 'startDate');
    return !isEqual(draftData, originData);
  }

  reset() {
    if (!this.origin) return this.toObject();
    return this.merge(this.origin).toObject();
  }

  syncToOrigin() {
    const { firstWeek, secondWeek, thirdWeek, forthWeek, scheduleType, startDate } = this.toObject();
    return this.set(
      'origin',
      cloneDeep({ firstWeek, secondWeek, thirdWeek, forthWeek, scheduleType, startDate }),
    ).toObject();
  }
}

export const staffScheduleWorkingHourMapBox = createRecordMapBox(
  'staffSchedule/regular/time',
  StaffScheduleWorkingHourRecord,
  'staffId',
);

export class StaffScheduleWorkingSlotRecord extends Record<StaffScheduleDraftData<StaffScheduleWorkingSlot>>({
  staffId: ID_ANONYMOUS,
  scheduleType: StaffScheduleType.Every1Week,
  startDate: dayjs().startOf('date'),
  endDate: MaxStaffEndDate,
  firstWeek: getDefaultWeekSlotValue(),
  secondWeek: getDefaultWeekSlotValue(),
  thirdWeek: getDefaultWeekSlotValue(),
  forthWeek: getDefaultWeekSlotValue(),
  origin: null,
}) {
  get isValidStaff() {
    return isNormal(this.staffId);
  }

  getWeekData(weekNum: WeekKeyMapNum) {
    const weekType = WeekKeyMapNum[Number(weekNum)] as WeekKeyTypes;
    return this[weekType];
  }

  /** 是否在循环的week时间段内 */
  isWithInCycleWeek(date = dayjs()) {
    const { startDate, endDate, scheduleType } = this;
    if (scheduleType === StaffScheduleType.Every1Week) {
      return true;
    }
    return date >= startDate && date <= endDate;
  }

  isDirty() {
    if (!this.origin) return false;

    const { scheduleType, firstWeek, secondWeek, thirdWeek, forthWeek } = this.toObject();
    const draftData = { scheduleType, firstWeek, secondWeek, thirdWeek, forthWeek };
    const originData = omit(this.origin, 'startDate');
    return !isEqual(draftData, originData);
  }

  syncToOrigin() {
    const { firstWeek, secondWeek, thirdWeek, forthWeek, scheduleType, startDate } = this.toObject();
    return this.set(
      'origin',
      cloneDeep({ firstWeek, secondWeek, thirdWeek, forthWeek, scheduleType, startDate }),
    ).toObject();
  }

  reset() {
    if (!this.origin) return this.toObject();
    return this.merge(this.origin).toObject();
  }
}

export const staffScheduleWorkingSlotMapBox = createRecordMapBox(
  'staffSchedule/regular/slot',
  StaffScheduleWorkingSlotRecord,
  'staffId',
);

export class StaffScheduleServiceAreaRecord extends Record<StaffScheduleDraftData<StaffScheduleServiceArea>>({
  staffId: ID_ANONYMOUS,
  scheduleType: StaffScheduleType.Every1Week,
  firstWeek: getDefaultServiceAreaValue(),
  secondWeek: getDefaultServiceAreaValue(),
  thirdWeek: getDefaultServiceAreaValue(),
  forthWeek: getDefaultServiceAreaValue(),
  startDate: dayjs().startOf('date'),
  endDate: MaxStaffEndDate,
  origin: null,
}) {
  /** staff 数据是否加载 */
  get isValidStaff() {
    return isNormal(this.staffId);
  }

  /** 是否在循环的week时间段内 */
  isWithInCycleWeek(date = dayjs()) {
    const { startDate, endDate, scheduleType } = this;
    return isWithInCycleWeek(date, startDate, endDate, scheduleType);
  }

  isDirty() {
    if (!this.origin) return false;

    const { scheduleType, firstWeek, secondWeek, thirdWeek, forthWeek } = this.toObject();
    const draftData = { scheduleType, firstWeek, secondWeek, thirdWeek, forthWeek };
    const originData = omit(this.origin, 'startDate');
    return !isEqual(draftData, originData);
  }

  syncToOrigin() {
    const { firstWeek, secondWeek, thirdWeek, forthWeek, scheduleType, startDate } = this.toObject();
    return this.set(
      'origin',
      cloneDeep({ firstWeek, secondWeek, thirdWeek, forthWeek, scheduleType, startDate }),
    ).toObject();
  }

  reset() {
    if (!this.origin) return this.toObject();
    return this.merge(this.origin).toObject();
  }
}

export const staffScheduleServiceAreaMapBox = createRecordMapBox(
  'staffSchedule/regular/serviceArea',
  StaffScheduleServiceAreaRecord,
  'staffId',
);

export class StaffDateOverrideRecord extends Record<DateOverrideRecord<StaffScheduleDateOverride>>({
  staffId: ID_ANONYMOUS,
  ongoing: [],
  history: [],
}) {}

export const staffScheduleDateOverrideMapBox = createRecordMapBox(
  'staffSchedule/date/override',
  StaffDateOverrideRecord,
  'staffId',
);

export class StaffSlotOverrideRecord extends Record<DateOverrideRecord<StaffScheduleSlotOverride>>({
  staffId: ID_ANONYMOUS,
  ongoing: [],
  history: [],
}) {
  get isValidRecord() {
    return isNormal(this.staffId);
  }
}
export const staffScheduleSlotOverrideMapBox = createRecordMapBox(
  'staffSchedule/slot/override',
  StaffSlotOverrideRecord,
  'staffId',
);

export class StaffAreaOverrideRecord extends Record<AreaOverrideRecord>({
  staffId: ID_ANONYMOUS,
  ongoing: [],
  history: [],
}) {
  get isValidRecord() {
    return isNormal(this.staffId);
  }
}

export const staffScheduleAreaOverrideMapBox = createRecordMapBox(
  'staffSchedule/area/override',
  StaffAreaOverrideRecord,
  'staffId',
);

export class BizScheduleWorkingHourRecord extends Record<BizScheduleWorkingHour>({
  businessId: ID_ANONYMOUS,
  timeData: getDefaultWeekTimeValueLegacy(),
}) {}

export const bizScheduleMapBox = createRecordMapBox(
  'bizSchedule/date/override',
  BizScheduleWorkingHourRecord,
  'businessId',
);

type CalendarViewDataRecordType = {
  staffId: number;
  workingHour: {
    [date: string]: WorkingHourValue;
  };
  scheduleType: number;
  startDate: Dayjs;
  area: OpenApiModels['POST/business/workingArea/shiftManagement']['Res'][number]['workingAreaRange'];
  overrideArea: OpenApiModels['POST/business/staff/override/area/shiftManagement']['Res'][number]['workingAreaRange'];
  ownKey: string;
};
export class CalendarViewDataRecord extends Record<CalendarViewDataRecordType>({
  staffId: ID_ANONYMOUS,
  workingHour: {},
  area: {},
  overrideArea: {},
  startDate: dayjs().startOf('date'),
  scheduleType: StaffScheduleType.Every1Week,
  ownKey: '',
}) {
  static ownKey(businessId: number, staffId: number) {
    return `${businessId}_${staffId}`;
  }
}

type CalendarViewSlotDataRecordType = {
  staffId: number;
  workingSlot: {
    [date: string]: WorkingSlotValue;
  };
  scheduleType: number;
  startDate: Dayjs;
  ownKey: string;
};

export class CalendarViewSlotDataRecord extends Record<CalendarViewSlotDataRecordType>({
  staffId: ID_ANONYMOUS,
  workingSlot: {},
  scheduleType: StaffScheduleType.Every1Week,
  startDate: dayjs().startOf('date'),
  ownKey: '',
}) {
  static ownKey(businessId: number, staffId: number) {
    return `${businessId}_${staffId}`;
  }
}

export const calendarViewDataMapBox = createRecordMapBox(
  'staffSchedule/calendar/view/data',
  CalendarViewDataRecord,
  'ownKey',
);

export const calendarViewSlotDataMapBox = createRecordMapBox(
  'staffSchedule/calendar/view/slot',
  CalendarViewSlotDataRecord,
  'ownKey',
);
