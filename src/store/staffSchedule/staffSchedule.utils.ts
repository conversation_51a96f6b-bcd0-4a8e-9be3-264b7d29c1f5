import {
  type TimeAvailabilityDay,
  type SlotAvailabilityDay,
} from '@moego/api-web/moego/models/organization/v1/staff_availability_models';
import { type ValidLimitationFormValue } from '../../container/settings/Settings/StaffSetting/ShiftManagement/components/Limitation/LimitationFormContext';
import { type FullWeekDay, FullWeekDayListMondayFirst } from '../onlineBooking/models/OnlineBookingPreference';
import { type WorkingSlotValue, type WorkingHourValue } from '../staff/staff.boxes';
import { type FullWeekData, type StaffScheduleBase, type RotateWeek, RotateWeekList } from './staffSchedule.types';

// 把 raw 转化成数组形式，共 4 x 7 = 28 周，用于转换 staff 的 availability 数据
const transformToDayList = <T, P>(
  raw: StaffScheduleBase<FullWeekData<T>>,
  transform: (
    raw: T,
    meta: {
      weekNum: number;
      dayNum: number;
    },
  ) => P,
) => {
  const { firstWeek, secondWeek, thirdWeek, forthWeek } = raw;

  return [firstWeek, secondWeek, thirdWeek, forthWeek].reduce((dayList, weekData, weekNum) => {
    const currentWeekDayList = FullWeekDayListMondayFirst.reduce((currWeekList, upperCaseDayKey, dayNum) => {
      const dayKey = upperCaseDayKey.toLowerCase() as Lowercase<FullWeekDay>;
      const dayData = weekData[dayKey];

      const transformedData = transform(dayData, {
        weekNum,
        dayNum,
      });

      currWeekList.push(transformedData);

      return currWeekList;
    }, [] as P[]);

    return dayList.concat(currentWeekDayList);
  }, [] as P[]);
};

/**
 * 把本地 working hour 数据转化成 availability 数据用于发送请求
 * @param raw
 * @returns
 */
export const transformWorkingHourMap2DayList = (raw: StaffScheduleBase<FullWeekData<WorkingHourValue>>) => {
  return transformToDayList(raw, (dayRaw, { weekNum, dayNum }) => {
    const { timeRange, limit, isAvailable } = dayRaw;

    return {
      dayOfWeek: dayNum + 1,
      scheduleType: weekNum + 1,
      isAvailable: isAvailable && timeRange.length > 0,
      timeDailySetting: {
        limit: limit as ValidLimitationFormValue,
      },
      timeHourSettingList: timeRange,
    };
  });
};

/**
 * 把本地 working slot 数据转化成 availability 数据用于发送请求
 * @param raw
 * @returns
 */
export const transformWorkingSlotMap2DayList = (raw: StaffScheduleBase<FullWeekData<WorkingSlotValue>>) => {
  return transformToDayList(raw, (dayRaw, { weekNum, dayNum }) => {
    const { slotDailySetting, slotHourSettingList, isAvailable } = dayRaw;

    return {
      dayOfWeek: dayNum + 1,
      scheduleType: weekNum + 1,
      isAvailable,
      slotDailySetting: {
        ...slotDailySetting,
        limit: slotDailySetting.limit as ValidLimitationFormValue,
      },
      slotHourSettingList: slotHourSettingList.map((slot) => ({
        ...slot,
        limit: slot.limit as ValidLimitationFormValue,
      })),
    };
  });
};

interface DayListItem {
  dayOfWeek: number; // 当前是第几天，monday: 1, tuesday: 2, ... sunday: 7
  scheduleType: number; // 当前是第几周，1: firstWeek, 2: secondWeek, 3: thirdWeek, 4: forthWeek
}
/**
 * 把 list 转化成 week map，一定是 4 周 x 7 天 = 28 项
 *
 * @param list 使用之前确保 list 为 28 项，否则数据可能出现错误
 * @param transform
 */
const transformToWeekMap = <T extends DayListItem, P>(
  list: T[],
  transform: (item: T) => P,
): {
  [key in RotateWeek]: FullWeekData<P>;
} => {
  return list.reduce(
    (weekMap, item) => {
      const { dayOfWeek, scheduleType } = item;
      const weekNum = scheduleType - 1;
      const dayNum = dayOfWeek - 1;

      const weekKey = RotateWeekList[weekNum];
      const dayKey = FullWeekDayListMondayFirst[dayNum].toLowerCase() as Lowercase<FullWeekDay>;

      const transformedDayData = transform(item);

      if (!weekMap[weekKey]) {
        weekMap[weekKey] = {
          [dayKey]: transformedDayData,
        };
      } else {
        weekMap[weekKey][dayKey] = transformedDayData;
      }

      return weekMap;
    },
    {} as {
      [key in RotateWeek]: Partial<FullWeekData<P>>;
    },
  ) as {
    [key in RotateWeek]: FullWeekData<P>;
  };
};

export const transformWeekDayList2WorkingHour = (list: TimeAvailabilityDay[]) => {
  return transformToWeekMap(list, (item) => {
    const { timeDailySetting, timeHourSettingList, isAvailable } = item;
    return {
      isAvailable: isAvailable && timeHourSettingList.length > 0,
      timeRange: timeHourSettingList,
      limit: timeDailySetting.limit,
    };
  });
};

export const transformWeekDayList2WorkingSlot = (list: SlotAvailabilityDay[]) => {
  return transformToWeekMap(list, (item) => item);
};
