import { Record } from 'immutable';
import { type WorkingHourTimeRange } from '../../staff/staff.boxes';
import { createRecordMapBox } from '../../utils/RecordMap';
import { createEnum } from '../../utils/createEnum';
import { ID_ANONYMOUS } from '../../utils/identifier';
import { createBox } from '../../utils/utils';
import {
  AvailableTimeTypeKind,
  BookingRangeEndKind,
  FakeItFilterKind,
  MostFarAvailableKind,
  type OnlineBookingPreferenceModel,
  SoonestAvailableKind,
  TimeSlotFormatKind,
} from '../models/OnlineBookingPreference';
import {
  getDefaultWeekSlotValue,
  getDefaultWeekTimeValue,
  type WeekSlotValue,
  type WeekTimeValue,
} from '../../staffSchedule/staffSchedule.types';
import { cloneDeep, isEqual } from 'lodash';

export interface StaffAvailableTimeModel {
  isSelected: boolean;
  limitIds: number[];
  timeRange: readonly WorkingHourTimeRange[];
}

export interface StaffAvailableSlot {
  startTime: number;
  capacity: number;
  limitIds: number[];
}

export interface StaffAvailableSlotModel {
  isSelected: boolean;
  timeSlot: readonly StaffAvailableSlot[];
}

export type DraftRecordProps<T> = T & {
  origin: T | null;
};

export type OBStaffWorkingHour = WeekTimeValue & {
  staffId: number;
};

export type OBStaffWorkingSlot = WeekSlotValue & {
  staffId: number;
};

export class OBStaffWorkingHourRecord extends Record<DraftRecordProps<OBStaffWorkingHour>>({
  origin: null,
  staffId: ID_ANONYMOUS,
  ...getDefaultWeekTimeValue(),
}) {
  isDirty() {
    if (!this.origin) {
      return false;
    }
    const { origin, ...draft } = this.toObject();
    return !isEqual(draft, origin);
  }

  reset() {
    if (!this.origin) return;
    return this.merge(this.origin).toObject();
  }

  syncToOrigin() {
    const { origin: _, ...draft } = this.toObject();
    return this.set('origin', cloneDeep(draft)).toObject();
  }
}

export const OBStaffWorkingHourMapBox = createRecordMapBox(
  'onlineBooking/workingHours',
  OBStaffWorkingHourRecord,
  'staffId',
);

export class OBStaffWorkingSlotRecord extends Record<DraftRecordProps<OBStaffWorkingSlot>>({
  origin: null,
  staffId: ID_ANONYMOUS,
  ...getDefaultWeekSlotValue(),
}) {
  // eslint-disable-next-line sonarjs/no-identical-functions
  isDirty() {
    if (!this.origin) {
      return false;
    }

    const { origin, ...draft } = this.toObject();
    return !isEqual(draft, origin);
  }

  reset() {
    if (!this.origin) return;
    return this.merge(this.origin).toObject();
  }

  syncToOrigin() {
    const { origin: _, ...draft } = this.toObject();
    return this.set('origin', cloneDeep(draft)).toObject();
  }
}

export const OBStaffWorkingSlotMapBox = createRecordMapBox(
  'onlineBooking/workingSlots',
  OBStaffWorkingSlotRecord,
  'staffId',
);

export type StaffIsAvailable = {
  staffId: number;
  isAvailable: boolean;
};
export class StaffIsAvailableRecord extends Record<DraftRecordProps<StaffIsAvailable>>({
  origin: null,
  staffId: ID_ANONYMOUS,
  isAvailable: false,
}) {
  // eslint-disable-next-line sonarjs/no-identical-functions
  isDirty() {
    if (!this.origin) {
      return false;
    }

    const { origin, ...draft } = this.toObject();
    return !isEqual(draft, origin);
  }

  reset() {
    if (!this.origin) return;
    return this.merge(this.origin).toObject();
  }

  syncToOrigin() {
    const { origin: _, ...draft } = this.toObject();
    return this.set('origin', cloneDeep(draft)).toObject();
  }
}

// create StaffIsAvailableListBox
export const staffIsAvailableMapBox = createRecordMapBox(
  'onlineBooking/staff/isAvailable/map',
  StaffIsAvailableRecord,
  'staffId',
);

/**
 * 这个应该是通用的
 */
export const NewClientFlowType = createEnum({
  phoneNumber: [
    0,
    {
      label: 'Enter phone number',
      tooltip:
        'As the first step in the online booking flow, visitors will enter their phone number. The system will then identify whether they are existing clients or new visitors.',
    },
  ] as const,
  verificationQuestion: [
    1,
    {
      label: 'Verification question',
      tooltip:
        'As the first step of the online booking flow, the system will ask visitors if they have ever booked with us before and allow them to manually select their answer.',
    },
  ] as const,
});

export interface AvailabilitySettings
  extends Pick<
    OnlineBookingPreferenceModel,
    | 'businessId'
    | 'availableTimeType'
    | 'bySlotTimeslotFormat'
    | 'bySlotTimeslotMins'
    | 'fakeIt'
    | 'showOneAvailableTime'
    | 'bySlotShowOneAvailableTime'
    | 'timeslotFormat'
    | 'timeslotMins'
    | 'displayStaffSelectionPage'
    | 'arrivalWindowBeforeMin'
    | 'arrivalWindowAfterMin'
    | 'bookingRangeStartOffset'
    | 'bookingRangeEndType'
    | 'bookingRangeEndOffset'
    | 'bookingRangeEndDate'
    | 'newClientFlowType'
  > {}

export class DraftAvailableSlotRecord extends Record<AvailabilitySettings>({
  businessId: ID_ANONYMOUS,
  availableTimeType: AvailableTimeTypeKind.ByWorkingHour,
  timeslotFormat: TimeSlotFormatKind.ExactTimes,
  timeslotMins: 60,
  bySlotTimeslotFormat: TimeSlotFormatKind.ExactTimes,
  bySlotTimeslotMins: 50,
  showOneAvailableTime: 0,
  bySlotShowOneAvailableTime: false,
  fakeIt: FakeItFilterKind.Off,
  displayStaffSelectionPage: true,
  arrivalWindowBeforeMin: 0,
  arrivalWindowAfterMin: 0,
  bookingRangeStartOffset: SoonestAvailableKind.SameDay,
  bookingRangeEndType: BookingRangeEndKind.ByPresetOffset,
  bookingRangeEndOffset: MostFarAvailableKind.HalfYear,
  bookingRangeEndDate: '',
  newClientFlowType: NewClientFlowType.phoneNumber,
}) {
  get isByWorkingHour() {
    return this.availableTimeType === AvailableTimeTypeKind.ByWorkingHour;
  }

  get isBySlot() {
    return this.availableTimeType === AvailableTimeTypeKind.BySlot;
  }

  get isDisableSelectTime() {
    return this.availableTimeType === AvailableTimeTypeKind.DisableSelectTime;
  }

  get activeTimeSlotFormat() {
    return this.isByWorkingHour ? this.timeslotFormat : this.isBySlot ? this.bySlotTimeslotFormat : undefined;
  }

  get paramsByWorkingHourOrBySlot() {
    const {
      displayStaffSelectionPage,
      arrivalWindowBeforeMin,
      arrivalWindowAfterMin,
      bookingRangeStartOffset,
      bookingRangeEndType,
      bookingRangeEndOffset,
      bookingRangeEndDate,
    } = this;
    return {
      displayStaffSelectionPage,
      arrivalWindowBeforeMin,
      arrivalWindowAfterMin,
      bookingRangeStartOffset,
      bookingRangeEndType,
      bookingRangeEndOffset,
      bookingRangeEndDate,
    };
  }

  get paramsByWorkingHour() {
    const { timeslotFormat, timeslotMins, showOneAvailableTime, fakeIt } = this;

    return {
      showOneAvailableTime,
      timeslotFormat,
      timeslotMins,
      fakeIt,
    };
  }

  get paramsBySlot() {
    const { bySlotTimeslotFormat, bySlotTimeslotMins, bySlotShowOneAvailableTime } = this;

    return {
      bySlotShowOneAvailableTime,
      bySlotTimeslotFormat,
      bySlotTimeslotMins,
    };
  }
}

export const availabilityDraftSettingsBox = createRecordMapBox(
  'onlineBooking/available/draft/box',
  DraftAvailableSlotRecord,
  'businessId',
);

export const availabilityRawSettingsBox = createRecordMapBox(
  'onlineBooking/available/raw/box',
  DraftAvailableSlotRecord,
  'businessId',
);

/** 是否同步staff的regular working hour */
export const syncWithStaffRegularWorkingHour = createBox('onlineBooking/available/sync/with/staff', false);
