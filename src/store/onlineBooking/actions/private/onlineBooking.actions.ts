/*
 * @since 2020-11-11 11:22:20
 * <AUTHOR> <<EMAIL>>
 */

import { type GroomingAutoAssignDetail } from '@moego/api-web/moego/api/online_booking/v1/booking_request_api';
import { ServiceItemType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type AcceptBookingRequestV2Request } from '@moego/api-web/moego/service/online_booking/v1/booking_request_service';
import { action } from 'amos';
import { type OverviewResProps } from '../../../../container/OnlineBooking/components/BookingOverview/BookingOverview.type';
import { http } from '../../../../middleware/api';
import { BookingRequestServiceClient } from '../../../../middleware/clients';
import { type OpenApiModels } from '../../../../openApi/schema';
import { currentBusinessIdBox } from '../../../business/business.boxes';
import { selectCurrentBusiness } from '../../../business/business.selectors';
import { GroomingTicketStatus } from '../../../grooming/grooming.boxes';
import {
  type SaveCustomerActiveSubscriptionListParam,
  saveCustomerActiveSubscriptionList,
} from '../../../membership/membership.actions';
import { createOnlineBookingObservableAction } from '../../../observableServices/observableServices';
import { type PagedInput } from '../../../utils/PagedList';
import { type PartialRequiredProps } from '../../../utils/RecordMap';
import { type EnumKeys } from '../../../utils/createEnum';
import { toNumber } from '../../../utils/identifier';
import { type MergedOBAddOnDetail, transformLatestRequest } from '../../models/OnlineBookingLatestRequest';
import { type OnlineBookingAppointmentPet, OnlineBookingRequestListType } from '../../models/OnlineBookingRequest';
import {
  OnlineBookingRequestListFilterRecord,
  OnlineBookingStaffAvailableConfigRecord,
  SortKeyMap,
  businessOnlineBookingRequestListBox,
  onlineBookingLatestRequestMapBox,
  onlineBookingNameListBox,
  onlineBookingRequestMapBox,
  onlineBookingStaffAvailableConfigMapBox,
} from '../../onlineBooking.boxes';
import { selectBusinessOnlineBookingRequests } from '../../onlineBooking.selectors';

export const getOnlineBookingLatestRequestDetail = createOnlineBookingObservableAction(
  'getOnlineBookingLatestRequestDetail',
  async (dispatch, _select, id: number) => {
    const r = await http.open('GET/grooming/ob/v2/ob-request', { id });
    const pets = r?.pets || [];
    const requestId = r?.apptId;
    // 旧 Grooming 接口只支持 1 个 service，需要从 pets 里面取 id 再匹配
    const services = r?.pets?.map((pet) => ({
      petId: String(pet.petId),
      ...(r?.services?.find((service) => service.serviceId === pet.serviceId) || {
        serviceName: '',
      }),
      serviceId: String(pet.serviceId),
    }));
    const addons: MergedOBAddOnDetail[] = r.services
      ?.filter((item) => item.serviceType === ServiceType.ADDON)
      .map((addOn) => {
        const currentPet = pets.find((pet) => pet.addOnIds.includes(addOn.serviceId));
        const fakeServiceDetailId = `${requestId}-${addOn.serviceId}-${currentPet?.petId || ''}`;
        return {
          ...addOn,
          id: fakeServiceDetailId,
          serviceDetailId: fakeServiceDetailId,
          servicePrice: addOn.price,
          bookingRequestId: `${requestId}`,
          addOnId: `${addOn.serviceId}`,
          petId: `${currentPet?.petId || ''}`,
          serviceItemType: ServiceItemType.GROOMING,
        };
      });
    dispatch(
      onlineBookingLatestRequestMapBox.mergeItem(id, {
        ...r,
        services,
        addons,
      }),
    );
  },
);

export const cancelOnlineBookingRequest = createOnlineBookingObservableAction(
  'cancelOnlineBookingRequest',
  async (dispatch, _select, { id, refundPrepaid = false }: { id: number; refundPrepaid?: boolean }) => {
    await http.open('PUT/grooming/appointment/cancel', {
      id,
      cancelByType: 0,
      noShow: 2,
      refundPrepaid,
    });
    dispatch(onlineBookingRequestMapBox.mergeItem(id, { status: GroomingTicketStatus.Cancelled }));
  },
);

export const moveOnlineBookingRequestToWaitingList = action(async (dispatch, _select, id: number) => {
  await http.open('PUT/grooming/appointment/waiting', { id });
  dispatch(onlineBookingRequestMapBox.mergeItem(id, { onlineBookingType: OnlineBookingRequestListType.WaitingList }));
});

/** 获取是否 开启/关闭 time/slot */
export const getOnlineBookingStaffAvailable = action(
  async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    const list = await http.open('GET/grooming/bookOnline/staff/availability', {});
    const newList = list.map((l) => ({
      ...l,
      ownId: OnlineBookingStaffAvailableConfigRecord.ownId(businessId, l.staffId),
    }));
    dispatch(onlineBookingStaffAvailableConfigMapBox.mergeItems(newList));
  },
);

export const getOnlineBookingOverview = createOnlineBookingObservableAction(
  'getOnlineBookingOverview',
  async (_dispatch, _select, input: OpenApiModels['POST/grooming/ob/v2/business/metrics']['Req']) => {
    const res = await http.open('POST/grooming/ob/v2/business/metrics', input);
    return res.reduce((prev, { name, ...rest }) => ({ ...prev, [name]: rest }), {} as OverviewResProps);
  },
);

export const getReviewUpdateDetailAction = action(
  async (_dispatch, _select, input: OpenApiModels['POST/grooming/ob/v2/client-pets-diff']['Req']) => {
    return await http.open('POST/grooming/ob/v2/client-pets-diff', input);
  },
);

export const submitReviewUpdateAction = action(
  async (_dispatch, _select, input: OpenApiModels['PUT/grooming/ob/v2/client-pets']['Req']) => {
    return await http.open('PUT/grooming/ob/v2/client-pets', input);
  },
);

export const dismissReviewUpdateAction = action(
  async (_dispatch, _select, input: OpenApiModels['DELETE/grooming/ob/v2/client-pets-diff']['Req']) => {
    return await http.open('PUT/grooming/ob/v2/client-pets', input);
  },
);

export const getOnlineBookingClientLocation = action(
  async (_dispatch, select, input: OpenApiModels['POST/business/staff/certainArea/locationQuery']['Req']) => {
    const business = select(selectCurrentBusiness());
    return await http.open('POST/business/staff/certainArea/locationQuery', input, {
      query: { businessId: business.id },
    });
  },
);

export const getOnlineBookingClientIsInside = action(
  async (_dispatch, _select, input: OpenApiModels['GET/grooming/distance']['Req']) => {
    return await http.open('GET/grooming/distance', input);
  },
);

export const getCompanyAllOnlineBookingNameList = action(async (dispatch) => {
  const r = await http.open('GET/grooming/ob/v2/business/brief/profile/list');
  dispatch(onlineBookingNameListBox.setState(r));
  return r;
});

export const getBookingRequestList = createOnlineBookingObservableAction(
  'getBookingRequestList',
  async (
    dispatch,
    select,
    input: PagedInput & PartialRequiredProps<OnlineBookingRequestListFilterRecord, 'type'>,
    businessId: number = select(currentBusinessIdBox),
  ) => {
    const state = select(selectBusinessOnlineBookingRequests(input.type!, businessId));
    const ownKey = OnlineBookingRequestListFilterRecord.ownKey(input.type!, businessId);
    const filter = state.getFilter(input);
    dispatch(businessOnlineBookingRequestListBox.updateItem(ownKey, (v) => v.applyStart(input)));

    try {
      const res = await BookingRequestServiceClient.getBookingRequestList({
        businessId: businessId.toString(),
        pagination: {
          pageNum: filter.pageNum,
          pageSize: filter.pageSize,
        },
        orderBys: [
          {
            fieldName: SortKeyMap.mapLabels[SortKeyMap[filter.orderBy as EnumKeys<typeof SortKeyMap>]],
            asc: filter.orderType === 'asc',
          },
        ],
        serviceTypeIncludes: [],
      });

      const membershipSubscriptionInfo: SaveCustomerActiveSubscriptionListParam[] = res.bookingRequestItems.map(
        (item) => {
          return {
            customerId: item.customerDetail.customerId,
            membershipSubscriptions: item.membershipSubscriptions?.membershipSubscriptions || [],
          };
        },
      );

      dispatch([
        businessOnlineBookingRequestListBox.updateItem(ownKey, (v) =>
          v.applySuccess(
            res.bookingRequestItems.map((item) => toNumber(item?.bookingRequest?.id)),
            Number(res.pagination.total),
            filter.pageNum,
            filter.clear,
          ),
        ),
        onlineBookingRequestMapBox.mergeItems(
          res.bookingRequestItems.map((item) => {
            const {
              bookingRequest,
              relatedMemberships,
              hasRequestUpdate,
              pay,
              serviceDetails,
              customerDetail: customer,
              incompleteDetails,
              customerPackages,
            } = item;
            const { source, sourceId } = bookingRequest;
            let autoAssign = {} as GroomingAutoAssignDetail;
            serviceDetails?.forEach((serviceDetail) => {
              serviceDetail?.services?.forEach((service) => {
                autoAssign = { ...autoAssign, ...service?.grooming?.autoAssign };
              });
            });

            const petList: OnlineBookingAppointmentPet[] = [];
            serviceDetails?.forEach((service) =>
              service.services.forEach((item) => {
                if (item?.grooming?.service && service.petDetail) {
                  const { endTime, serviceId, serviceName, servicePrice, serviceTime, staffId, startTime, id } =
                    item?.grooming?.service || {};
                  const { breed: petBreed, id: petId, petName } = service.petDetail || {};

                  petList.push({
                    petId: toNumber(petId),
                    serviceId: toNumber(serviceId),
                    staffId: toNumber(staffId),
                    staffIds: [toNumber(staffId)],
                    petDetailId: toNumber(id),
                    endTime,
                    serviceName,
                    servicePrice,
                    serviceTime,
                    startTime,
                    petBreed,
                    petName,
                  });
                }
              }),
            );
            return {
              specificDates: bookingRequest.specificDates,
              serviceItemTypes: bookingRequest.serviceItemTypes,
              id: toNumber(bookingRequest?.id),
              autoAssign,
              additionalNoteTxt: bookingRequest?.additionalNote,
              services: serviceDetails,
              customerId: toNumber(customer?.customerId),
              onlineBookingType: input.type,
              hasRequestUpdate,
              groomingId: toNumber(bookingRequest.id),
              requestId: toNumber(bookingRequest.id),
              appointmentId: toNumber(bookingRequest.appointmentId),
              createTime: bookingRequest?.createdAt,
              appointmentDate: bookingRequest?.startDate,
              appointmentStartTime: bookingRequest?.startTime,
              appointmentEndTime: bookingRequest?.endTime,
              appointmentEndDate: bookingRequest?.endDate,
              noStartTime: bookingRequest?.noStartTime,
              bookOnlineStatus: bookingRequest?.status,
              petIds: serviceDetails?.map((item) => item?.petDetail?.id),
              customer,
              customerPackages,
              prepayStatus: pay?.prePayStatus,
              prepayRate: pay?.prePayRate,
              prepaidAmount: pay?.prePayAmount,
              enablePreAuth: pay?.preAuthEnable,
              staffId: toNumber(bookingRequest?.staffId),
              incompleteDetails,
              petList,
              source,
              sourceId,
              relatedMemberships,
            };
          }),
        ),
        saveCustomerActiveSubscriptionList(membershipSubscriptionInfo),
      ]);
    } catch (error) {
      dispatch(businessOnlineBookingRequestListBox.updateItem(ownKey, (v) => v.applyFail(filter.pageNum)));
      throw error;
    }
  },
);

export const getBookingRequest = createOnlineBookingObservableAction(
  'getBookingRequest',
  async (dispatch, select, id: number) => {
    const res = await BookingRequestServiceClient.getBookingRequest({ id: id.toString() });
    const request = transformLatestRequest(res, select);

    dispatch(onlineBookingLatestRequestMapBox.mergeItem(request.requestId, request));
  },
);

export const acceptBookingRequest = createOnlineBookingObservableAction(
  'acceptBookingRequest',
  async (dispatch, _select, input: AcceptBookingRequestV2Request) => {
    const res = await BookingRequestServiceClient.acceptBookingRequestV2(input);
    dispatch(onlineBookingRequestMapBox.mergeItem(toNumber(input.id), { status: GroomingTicketStatus.Cancelled }));
    return res;
  },
);

export const declineBookingRequest = createOnlineBookingObservableAction(
  'declineBookingRequest',
  async (_dispatch, _select, id: string) => {
    const res = await BookingRequestServiceClient.declineBookingRequest({ id });
    return res;
  },
);

export const autoAssignOBRequestLodging = action(async (_dispatch, _select, bookingRequestId: string) => {
  const res = await BookingRequestServiceClient.autoAssignV2({
    bookingRequestId,
  });
  return res;
});

export const moveBDRequestToWaitlist = action(async (_dispatch, select, bookingRequestId: string) => {
  const businessId = select(currentBusinessIdBox);
  const res = await BookingRequestServiceClient.moveBookingRequestToWaitlist({
    bookingRequestId,
    businessId: `${businessId}`,
  });
  return res;
});
