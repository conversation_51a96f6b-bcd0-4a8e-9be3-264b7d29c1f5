import { ServiceItemType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { type GroomingServiceAvailabilityUpdateDef } from '@moego/api-web/moego/models/online_booking/v1/ob_availability_setting_defs';
import { action, type Mutation } from 'amos';
import { cloneDeep, pick } from 'lodash';
import { toastApi } from '../../../../components/Toast/Toast';
import { http } from '../../../../middleware/api';
import { OBAvailabilitySettingClient, OBStaffAvailabilityServiceClient } from '../../../../middleware/clients';
import { currentBusinessIdBox } from '../../../business/business.boxes';
import { createOnlineBookingObservableAction } from '../../../observableServices/observableServices';
import { selectBusinessStaffs } from '../../../staff/staff.selectors';
import {
  type FullWeekDay,
  FullWeekDayListMondayFirst,
  type OnlineBookingPreferenceModel,
} from '../../models/OnlineBookingPreference';
import { draftOBSharedClientPetInfoBox } from '../../settings/availability.boxes';
import { selectDraftAcceptClientType, selectDraftAcceptPetIds } from '../../settings/availability.selectors';
import {
  type AvailabilitySettings,
  type OBStaffWorkingHour,
  type OBStaffWorkingSlot,
  type DraftRecordProps,
  OBStaffWorkingHourMapBox,
  OBStaffWorkingSlotMapBox,
  staffIsAvailableMapBox,
  availabilityDraftSettingsBox,
  availabilityRawSettingsBox,
  syncWithStaffRegularWorkingHour,
  type StaffIsAvailable,
} from '../../settings/availabilityGrooming.boxes';
import {
  setOBSharedClientAcceptId,
  setOBSharedNewClientFlowType,
  setOBSharedPetAcceptIds,
} from './availability.actions';
import { updateOnlineBookingPreference } from './onlineBookingPreference.actions';
import {
  type SlotAvailabilityDayDef,
  type TimeAvailabilityDayDef,
} from '@moego/api-web/moego/models/organization/v1/staff_availability_def';
import type { ValidLimitationFormValue } from '../../../../container/settings/Settings/StaffSetting/ShiftManagement/components/Limitation/LimitationFormContext';
import { getOnlineBookingStaffAvailable } from './onlineBooking.actions';

export const getOBStaffAvailability = action(async (dispatch, select, staffIdList: number[] = []) => {
  const businessId = select(currentBusinessIdBox);
  const res = await OBStaffAvailabilityServiceClient.getStaffAvailability({
    staffIdList: staffIdList.map((id) => `${id}`),
    businessId: `${businessId}`,
  });

  const { obStaffWorkingHourList, obStaffWorkingSlotList, staffIsAvailableList } = res.staffAvailabilityList.reduce(
    (list, next) => {
      const { staffId, timeAvailabilityDayList, slotAvailabilityDayList, isAvailable } = next;
      const weekHourData = FullWeekDayListMondayFirst.reduce((data, day, index) => {
        const dayOfWeek = index + 1;
        const timeAvailabilityDay = timeAvailabilityDayList.find((i) => i.dayOfWeek === dayOfWeek);
        if (!timeAvailabilityDay) return data;

        const { timeHourSettingList, timeDailySetting, isAvailable } = timeAvailabilityDay;

        const dayKey = day.toLowerCase() as Lowercase<FullWeekDay>;
        data[dayKey] = {
          isAvailable: isAvailable && timeHourSettingList.length > 0,
          timeRange: timeHourSettingList,
          limit: timeDailySetting.limit,
        };

        return data;
      }, {} as OBStaffWorkingHour);

      const weekSlotData = FullWeekDayListMondayFirst.reduce((data, day, index) => {
        const dayOfWeek = index + 1;
        const slotAvailabilityDay = slotAvailabilityDayList.find((i) => i.dayOfWeek === dayOfWeek);
        if (!slotAvailabilityDay) return data;

        const { slotDailySetting, slotHourSettingList, isAvailable } = slotAvailabilityDay;

        const dayKey = day.toLowerCase() as Lowercase<FullWeekDay>;

        data[dayKey] = {
          isAvailable,
          slotDailySetting,
          slotHourSettingList,
        };
        return data;
      }, {} as OBStaffWorkingSlot);

      const staffWorkingHour = {
        ...weekHourData,
        staffId: Number(staffId),
      };

      const staffWorkingSlot = {
        ...weekSlotData,
        staffId: Number(staffId),
      };

      const staffIsAvailable = {
        staffId: Number(staffId),
        isAvailable,
      };

      list.obStaffWorkingHourList.push({
        ...staffWorkingHour,
        origin: cloneDeep(staffWorkingHour),
      });
      list.obStaffWorkingSlotList.push({
        ...staffWorkingSlot,
        origin: cloneDeep(staffWorkingSlot),
      });
      list.staffIsAvailableList.push({
        origin: staffIsAvailable,
        ...staffIsAvailable,
      });

      return list;
    },
    {
      obStaffWorkingHourList: [],
      obStaffWorkingSlotList: [],
      staffIsAvailableList: [],
    } as {
      obStaffWorkingHourList: DraftRecordProps<OBStaffWorkingHour>[];
      obStaffWorkingSlotList: DraftRecordProps<OBStaffWorkingSlot>[];
      staffIsAvailableList: DraftRecordProps<StaffIsAvailable>[];
    },
  );

  dispatch([
    OBStaffWorkingHourMapBox.mergeItems(obStaffWorkingHourList),
    OBStaffWorkingSlotMapBox.mergeItems(obStaffWorkingSlotList),
    staffIsAvailableMapBox.mergeItems(staffIsAvailableList),
  ]);

  return {
    obStaffWorkingHourList,
    obStaffWorkingSlotList,
    staffIsAvailableList,
  };
});

/** 配置项(timeType/how soon/how far) 草稿态 */
export const setAvailabilityDraftSettings = action(
  async (dispatch, select, input: Partial<AvailabilitySettings>, businessId: number = select(currentBusinessIdBox)) => {
    dispatch([availabilityDraftSettingsBox.mergeItem(businessId, input)]);
  },
);

/** 配置项(timeType/how soon/how far) 初始值 */
export const setAvailabilityRawSettings = action(
  async (dispatch, select, input: Partial<AvailabilitySettings>, businessId: number = select(currentBusinessIdBox)) => {
    dispatch([availabilityRawSettingsBox.mergeItem(businessId, input)]);
  },
);

/** 配置项(timeType/how soon/how far) 重置部分草稿态字段 */
export const resetAvailabilityDraftSettingsProperty = action(
  async (dispatch, select, ...input: (keyof AvailabilitySettings)[]) => {
    const businessId: number = select(currentBusinessIdBox);
    const rawSettings = select(availabilityRawSettingsBox.mustGetItem(businessId));
    const selectedRawSettings = pick(rawSettings, input);
    dispatch([availabilityDraftSettingsBox.mergeItem(businessId, selectedRawSettings)]);
  },
);

/** 获取配置项(timeType/how soon/how far) */
export const getInitAvailableSettings = createOnlineBookingObservableAction(
  'getInitAvailableSettings',
  async (dispatch, _select) => {
    const {
      data: { bookOnlineInfo },
    } = await http.open('GET/grooming/bookOnline/setting/info', {
      withoutClientNotification: true,
    });
    const copyKeys: (keyof OnlineBookingPreferenceModel)[] = [
      'availableTimeType',
      'bySlotTimeslotFormat',
      'bySlotTimeslotMins',
      'fakeIt',
      'showOneAvailableTime',
      'bySlotShowOneAvailableTime',
      'timeslotFormat',
      'timeslotMins',
      'displayStaffSelectionPage',
      'arrivalWindowAfterMin',
      'arrivalWindowBeforeMin',
      'bookingRangeStartOffset',
      'bookingRangeEndType',
      'bookingRangeEndOffset',
      'bookingRangeEndDate',
    ];
    const params: Partial<AvailabilitySettings> = copyKeys.reduce(
      (pre, nextKey) => {
        return {
          ...pre,
          [nextKey]: bookOnlineInfo[nextKey as keyof AvailabilitySettings],
        };
      },
      {} as Partial<OnlineBookingPreferenceModel>,
    );
    dispatch([setAvailabilityDraftSettings(params), setAvailabilityRawSettings(params)]);
  },
);

/** 同步草稿态 by working hour/by slot */
// 点击 update 后，要看当前是什么类型，如果是 bySlot，则同步 bySlot 数据、回退 byTime 数据
export const syncTeamSchedule = action((dispatch, select) => {
  const staffIds = select(selectBusinessStaffs());
  const businessId = select(currentBusinessIdBox);
  const { isByWorkingHour, isBySlot, isDisableSelectTime } = select(
    availabilityDraftSettingsBox.mustGetItem(businessId),
  );

  if (isDisableSelectTime) return;
  const staffIsAvailableList = staffIds
    .map((staffId) => select(staffIsAvailableMapBox.mustGetItem(staffId)).syncToOrigin())
    .toArray();

  const actions: Mutation[] = [staffIsAvailableMapBox.mergeItems(staffIsAvailableList)];
  // by working hour: sync by working hour data & reset by slot data
  if (isByWorkingHour) {
    const times = staffIds
      .map((staffId) => select(OBStaffWorkingHourMapBox.mustGetItem(staffId)).syncToOrigin())
      .toArray();
    const slots = staffIds
      .map((staffId) => select(OBStaffWorkingSlotMapBox.mustGetItem(staffId)).reset())
      .toArray()
      .filter(Boolean) as DraftRecordProps<OBStaffWorkingSlot>[];
    actions.push(OBStaffWorkingHourMapBox.mergeItems(times), OBStaffWorkingSlotMapBox.mergeItems(slots));
  }

  // by slot: sync by slot data & reset by working hour data
  if (isBySlot) {
    const times = staffIds
      .map((staffId) => select(OBStaffWorkingHourMapBox.mustGetItem(staffId)).reset())
      .toArray()
      .filter(Boolean) as DraftRecordProps<OBStaffWorkingHour>[];
    const slots = staffIds
      .map((staffId) => select(OBStaffWorkingSlotMapBox.mustGetItem(staffId)).syncToOrigin())
      .toArray();
    actions.push(OBStaffWorkingHourMapBox.mergeItems(times), OBStaffWorkingSlotMapBox.mergeItems(slots));
  }

  dispatch(actions);
});

/** batch update working hour/slot */
export const updateTeamSchedule = action(
  async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    const staffIds = select(selectBusinessStaffs()).toArray();
    const { isByWorkingHour, isBySlot, isDisableSelectTime } = select(
      availabilityDraftSettingsBox.mustGetItem(businessId),
    );

    /**
     * logic:
     * 1. 如果 isDisableSelectTime 为 true，不更新 staffAvailability 数据
     * 2. 如果 isSync 为 true，只更新 staff is availability
     * 3. 如果 isByWorkingHour，只更新 staff is availability 和 timeAvailabilityDayList
     * 4. 如果 isBySlot，只更新 staff is availability 和 slotAvailabilityDayList
     * 5. 更新完毕后，要把对应的 draft 数据同步给 origin（减少请求发送，前端交互更流畅）
     */
    if (isDisableSelectTime) return;

    const staffAvailabilityList = staffIds.map((staffId) => {
      const staffIsAvailable = select(staffIsAvailableMapBox.mustGetItem(staffId)).toObject();
      const staffWorkingHour = select(OBStaffWorkingHourMapBox.mustGetItem(staffId)).toObject();
      const staffWorkingSlot = select(OBStaffWorkingSlotMapBox.mustGetItem(staffId)).toObject();

      // generate request body
      const { slotAvailabilityDayList, timeAvailabilityDayList } = FullWeekDayListMondayFirst.reduce(
        (map, dayKey, index) => {
          const lowerCaseDayKey = dayKey.toLowerCase() as Lowercase<FullWeekDay>;
          const dayOfWeek = index + 1;

          if (isBySlot) {
            // working slot data
            const slotAvailability = staffWorkingSlot[lowerCaseDayKey];
            const { slotDailySetting, slotHourSettingList } = slotAvailability;
            map.slotAvailabilityDayList.push({
              dayOfWeek,
              ...slotAvailability,
              slotDailySetting: {
                ...slotDailySetting,
                limit: slotDailySetting.limit as ValidLimitationFormValue,
              },
              slotHourSettingList: slotHourSettingList.map((item) => ({
                ...item,
                limit: item.limit as ValidLimitationFormValue,
              })),
            });
          }

          if (isByWorkingHour) {
            // working hour data
            const timeAvailability = staffWorkingHour[lowerCaseDayKey];
            const { timeRange, limit } = timeAvailability;
            map.timeAvailabilityDayList.push({
              dayOfWeek,
              isAvailable: timeRange.length > 0,
              timeDailySetting: {
                limit: limit as ValidLimitationFormValue,
              },
              timeHourSettingList: timeRange,
            });
          }

          return map;
        },
        {
          slotAvailabilityDayList: [],
          timeAvailabilityDayList: [],
        } as {
          slotAvailabilityDayList: SlotAvailabilityDayDef[];
          timeAvailabilityDayList: TimeAvailabilityDayDef[];
        },
      );

      return {
        staffId: `${staffId}`,
        isAvailable: staffIsAvailable.isAvailable,
        slotAvailabilityDayList,
        timeAvailabilityDayList,
      };
    });

    await OBStaffAvailabilityServiceClient.updateStaffAvailability({
      staffAvailabilityList,
      businessId: `${businessId}`,
    });

    dispatch(syncTeamSchedule());
  },
);

export const updateAvailabilitySettings = action(
  async (dispatch, select, businessId: number = select(currentBusinessIdBox)) => {
    const settings = select(availabilityDraftSettingsBox.mustGetItem(businessId));

    const params = settings.isDisableSelectTime
      ? {}
      : settings.isByWorkingHour
        ? {
            ...settings.paramsByWorkingHour,
            ...settings.paramsByWorkingHourOrBySlot,
          }
        : { ...settings.paramsBySlot, ...settings.paramsByWorkingHourOrBySlot };

    await dispatch(
      updateOnlineBookingPreference({
        ...params,
        availableTimeType: settings.availableTimeType,
      }),
    );

    dispatch([availabilityRawSettingsBox.mergeItem(businessId, settings.toObject())]);
  },
);

export const updateNewClientFlowType = action(async (dispatch, select) => {
  const { draftNewClientFlowType } = select(draftOBSharedClientPetInfoBox);
  await dispatch(
    updateOnlineBookingPreference({
      newClientFlowType: draftNewClientFlowType,
    }),
  );
  dispatch(setOBSharedNewClientFlowType(draftNewClientFlowType));
});

export const updatePetTypeAvailability = action((dispatch, select) => {
  const acceptPetIds = select(selectDraftAcceptPetIds(ServiceItemType.GROOMING));

  if (acceptPetIds.length === 0) {
    toastApi.neutral('Please select at least one pet type');
    return false;
  }

  return http
    .open('PUT/customer/pet/type/list', { ids: acceptPetIds })
    .then(() => dispatch(setOBSharedPetAcceptIds(acceptPetIds, ServiceItemType.GROOMING)));
});

export const submitAvailability = createOnlineBookingObservableAction(
  'submitAvailability',
  async (dispatch, _select) => {
    const updatePetTypeRes = dispatch(updatePetTypeAvailability());
    if (updatePetTypeRes === false) {
      return false;
    }

    await Promise.all([
      updatePetTypeRes,
      dispatch(updateGroomingOnlineAvailabilitySettings()),
      dispatch(updateNewClientFlowType()),
      dispatch(updateTeamSchedule()),
      dispatch(updateAvailabilitySettings()),
    ]);

    // ob grooming 修改 availability 后，重新获取一次 staff availability
    await dispatch(getOnlineBookingStaffAvailable());
    return true;
  },
);

export const getSyncStaffAvailabilityState = action(async (dispatch, _select) => {
  const flag = await http.open('GET/grooming/bookOnline/staff/availability/sync');
  dispatch(syncWithStaffRegularWorkingHour.setState(flag));
  return flag;
});

export const syncStaffAvailabilityState = action(async (dispatch, _select) => {
  await http.open('POST/grooming/bookOnline/staff/availability/sync');
  await dispatch(getSyncStaffAvailabilityState());
});

/**
 * 以下为新的接口，目前只支持 acceptCustomerType（BD 全都走新接口，Grooming 与老逻辑共存）
 */
export const getOnlineBookingGroomingAvailabilitySettings = action(
  async (dispatch, select, businessId = select(currentBusinessIdBox)) => {
    const res = await OBAvailabilitySettingClient.getGroomingServiceAvailability({
      businessId: `${businessId}`,
    });
    dispatch([setOBSharedClientAcceptId(res.availability.acceptCustomerType, ServiceItemType.GROOMING)]);
  },
);

export const updateGroomingOnlineAvailabilitySettings = action(
  async (dispatch, select, businessId = select(currentBusinessIdBox)) => {
    const acceptCustomerType = select(selectDraftAcceptClientType(ServiceItemType.GROOMING));
    const params: GroomingServiceAvailabilityUpdateDef = {
      acceptCustomerType,
    };
    await OBAvailabilitySettingClient.updateGroomingServiceAvailability({
      businessId: `${businessId}`,
      availability: params,
    });
    await dispatch(getOnlineBookingGroomingAvailabilitySettings());
    return true;
  },
);
