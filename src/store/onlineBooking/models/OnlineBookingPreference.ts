import { Record } from 'immutable';
import { appendQuery } from 'monofile-utilities/lib/query-string';
import { URL_ONLINE_BOOKING } from '../../../config/host/const';
import { type Replace } from '../../../config/interface';
import { type OpenApiModels } from '../../../openApi/schema';
import { createMap } from '../../../utils/createMap';
import { type EnumValues, createEnum } from '../../utils/createEnum';
import { ID_ANONYMOUS } from '../../utils/identifier';

export const enum FullWeekDay {
  Monday = 'Monday',
  Tuesday = 'Tuesday',
  Wednesday = 'Wednesday',
  Thursday = 'Thursday',
  Friday = 'Friday',
  Saturday = 'Saturday',
  Sunday = 'Sunday',
}

export const FullWeekDayList = [
  FullWeekDay.Sunday,
  FullWeekDay.Monday,
  FullWeekDay.Tuesday,
  FullWeekDay.Wednesday,
  FullWeekDay.Thursday,
  FullWeekDay.Friday,
  FullWeekDay.Saturday,
];

/**
 * 当后端接口出现以 index number 表示 dayOfWeek 时，要注意顺序，后端返回的 dayOfWeek 是 monday first 的
 * monday - 1, tuesday - 2, ... sunday - 7
 */
export const FullWeekDayListMondayFirst = [
  FullWeekDay.Monday,
  FullWeekDay.Tuesday,
  FullWeekDay.Wednesday,
  FullWeekDay.Thursday,
  FullWeekDay.Friday,
  FullWeekDay.Saturday,
  FullWeekDay.Sunday,
];

export interface BusinessHourModel {
  isSelected: boolean;
  timeRange: { startTime: number; endTime: number }[];
}

export type GalleryItemModel = OpenApiModels['GET/grooming/bookOnline/gallery']['Res']['data'][0];
export const BookingRangeEndKind = createEnum({
  ByPresetOffset: [1, 'By preset offset'],
  BySpecificDate: [2, 'By specific date'],
});
// 0-Same day,  1-next day,  2-2 days out,  3-3 days out
export const SoonestAvailableKind = createEnum({
  SameDay: [0, 'Same day'],
  NextDay: [1, 'Next day'],
  TwoDays: [2, '2 days out'],
  ThreeDays: [3, '3 days out'],
});
// 1-1months   2-2months 3-3months  6-6months
export const MostFarAvailableKind = createEnum({
  OneMonth: [30, '1 month'],
  TwoMonths: [60, '2 months'],
  Quarter: [90, '3 months'],
  HalfYear: [180, '6 months'],
  NineMonths: [270, '9 months'],
  OneYear: [365, '1 year'],
});
/**
 * ob 3.0 使用
 */
export const TimeSlotFormatKind = createEnum({
  ExactTimes: [1, 'Exact times'],
  ArrivalWindows: [2, 'Arrival windows'],
  DateOnly: [3, 'Date only'],
});
export const FakeItFilterKind = createEnum({
  Off: [0, { name: 'Off', label: 'Do not apply any filters to your availability' }],
  SlightlyBusy: [1, { name: 'Slightly busy', label: 'We’ll remove roughly 25% of your openings' }],
  ModeratelyBusy: [2, { name: 'Moderately busy', label: 'We’ll remove roughly 40% of your openings' }],
  ExtremelyBusy: [3, { name: 'Extremely busy', label: 'We’ll remove roughly 50% of your openings' }],
});
export const AcceptNotificationKind = createEnum({
  Email: [1, 'Email'],
  Text: [2, 'Text'],
  Both: [3, 'Email and text'],
  None: [4, 'None'],
});

// 这里的定义和后端 ShiftManagement 的 availabilityType 不同
// TODO: 单独提 PR 修改，以免出问题
export const AvailableTimeTypeKind = createEnum({
  ByWorkingHour: [0, 'By working hours'],
  BySlot: [1, 'By slots'],
  DisableSelectTime: [2, 'Disable select time option'],
});
export const ServiceFilterKind = createEnum({
  Closed: [0, 'closed'],
  Open: [1, 'open'],
});
export const NoShowProtectType = createEnum({
  Closed: [0, 'Closed'],
  CardOnFile: [1, 'Require card on file'],
  Prepayment: [2, 'Require prepayment'],
  PreAuth: [3, 'Require pre-authorization'],
} as const);
export type TNoShowProtectType = EnumValues<typeof NoShowProtectType>;
export const PrepaymentType = createEnum({
  PartialPay: [1, 'Partial amount (Deposit)'],
  FullPay: [0, 'Full amount'],
});
export const DepositType = createEnum({
  ByAmount: [0, 'By amount'],
  ByPercentage: [1, 'By percentage'],
});

export enum USE_OB_SETTING_VERSION {
  /** 老ob */
  DEFAULT_OB = 1,
  /** 新ob */
  NEW_OB = 2,
}

type NarrowPaymentTypeField<T extends { paymentType: number }> = Replace<T, { paymentType: TNoShowProtectType }>;

type OnlineBookingPreferenceProfilePart = OpenApiModels['GET/grooming/bookOnline/profile']['Res']['data'];
type OnlineBookingPreferenceInfoPart = Omit<
  OpenApiModels['GET/grooming/bookOnline/setting/info']['Res']['data']['bookOnlineInfo'],
  // Why omitting "createTime" and "updateTime":
  // The legacy number-typed "createTime" and "updateTime" are defined in OnlineBookingPreferenceProfilePart above, so
  // the new string-typed fields have to be omitted.
  'createTime' | 'updateTime' | 'smartScheduleMaxDist' | 'smartScheduleMaxTime' | 'paymentOptionMap'
>;
type OnlineBookingPreferenceNotificationPart =
  OpenApiModels['GET/grooming/bookOnline/setting/info']['Res']['data']['bookOnlineNotification'];
type OnlineBookingPreferencePaymentSettingRaw =
  OpenApiModels['GET/grooming/bookOnline/setting/info']['Res']['data']['paymentSetting'];
type OnlineBookingPreferencePaymentSetting = Replace<
  NarrowPaymentTypeField<OnlineBookingPreferencePaymentSettingRaw>,
  {
    certainGroupSetting?: NarrowPaymentTypeField<
      NonNullable<OnlineBookingPreferencePaymentSettingRaw['certainGroupSetting']>
    >;
  }
>;

export interface OnlineBookingPreferenceModel
  extends OnlineBookingPreferenceProfilePart,
    OnlineBookingPreferenceInfoPart,
    OnlineBookingPreferenceNotificationPart {
  paymentSetting: OnlineBookingPreferencePaymentSetting;
  galleryList: GalleryItemModel[];
  businessDescription: string;
  businessId: number;
  businessHours: globalThis.Record<FullWeekDay, BusinessHourModel>;
}

export class OnlineBookingPreferenceRecord extends Record<OnlineBookingPreferenceModel>({
  id: ID_ANONYMOUS,
  businessHours: createMap(FullWeekDayList, () => ({ isSelected: false, timeRange: [] })),
  businessHoursJson: '',
  businessName: '',
  phoneNumber: '',
  website: '',
  address: '',
  businessEmail: '',
  businessDescription: '',
  facebook: '',
  instagram: '',
  google: '',
  yelp: '',
  other: '',
  language: '',
  buttonColor: '',
  avatarPath: '',
  isEnable: 0,
  allowedSimplifySubmit: 0,
  maxAvailableDist: 0,
  maxAvailableTime: 0,
  /**
   * @deprecated migrated to bookingRangeStartOffset
   */
  soonestAvailable: SoonestAvailableKind.SameDay,
  /**
   * @deprecated migrated to bookingRangeEndOffset
   */
  farestAvailable: MostFarAvailableKind.HalfYear,
  createTime: 0,
  updateTime: 0,
  zipCode: '',
  placeName: '',
  state: '',
  stateAbbreviation: '',
  county: '',
  isNeedAddress: 0,
  isNeedSelectTime: 0,
  smartScheduleEnable: 0,
  fakeIt: FakeItFilterKind.Off,
  enableNoShowFee: 0,
  noShowFee: 0,
  appointmentInterval: 0,
  timeslotMins: 60,
  cancellationPolicy: '',
  acceptClient: 0,
  autoMoveWait: 0,
  serviceAreaEnable: 0,
  needWithinArea: 0,
  isByZipcode: 0,
  isByRadius: 0,
  settingLocation: '',
  settingLat: '',
  settingLng: '',
  isCheckExistingClient: 0,
  isRedirect: 0,
  bookOnlineName: '',
  description: '',
  weightLimitNotify: 0,
  weightLimit: 0,
  overLimitTips: '',
  showOneAvailableTime: 0,
  bySlotShowOneAvailableTime: false,
  galleryList: [],
  businessId: ID_ANONYMOUS,
  autoAccept: 0,
  timeslotFormat: TimeSlotFormatKind.ExactTimes,
  zipCodes: '',
  bySlotTimeslotFormat: TimeSlotFormatKind.ExactTimes,
  bySlotTimeslotMins: 60,
  /**
   * @deprecated migrated to bookingRangeStartOffset
   */
  bySlotSoonestAvailable: SoonestAvailableKind.SameDay,
  /**
   * @deprecated migrated to bookingRangeEndOffset
   */
  bySlotFarthestAvailable: MostFarAvailableKind.HalfYear,
  availableTimeType: AvailableTimeTypeKind.ByWorkingHour,
  serviceFilter: ServiceFilterKind.Closed,
  depositAmount: 0,
  depositPercentage: 0,
  prepayType: PrepaymentType.FullPay,
  depositType: DepositType.ByAmount,
  prepayTipEnable: 0,
  prepayPolicy: '',
  useVersion: ID_ANONYMOUS,
  preAuthTipEnable: 0,
  preAuthPolicy: '',
  autoRefundDeposit: 0,
  isRequireAgreement: 0,
  requestSubmittedAutoType: '',
  addressDetails: {
    address1: '',
    address2: '',
    city: '',
    country: '',
    state: '',
    lat: '',
    lng: '',
    zipcode: '',
  },
  displayStaffSelectionPage: true,
  arrivalWindowAfterMin: 0,
  arrivalWindowBeforeMin: 0,
  bookingRangeEndType: BookingRangeEndKind.ByPresetOffset,
  bookingRangeStartOffset: SoonestAvailableKind.SameDay,
  bookingRangeEndOffset: MostFarAvailableKind.HalfYear,
  bookingRangeEndDate: '',
  isNeedSendRenewNotification: false,
  serviceAreas: [],
  paymentSetting: {
    paymentType: 0,
    prepayType: PrepaymentType.FullPay,
    prepayTipEnable: 0,
    depositType: DepositType.ByAmount,
    depositAmount: 0,
    depositPercentage: 0,
    prepayPolicy: '',
    preAuthTipEnable: 0,
    preAuthPolicy: '',
    cancellationPolicy: '',
    certainGroupSetting: undefined,
  },
  companyId: '',

  acceptBusinessType: AcceptNotificationKind.None,
  autoMoveBusinessType: AcceptNotificationKind.None,
  submitBusinessType: AcceptNotificationKind.None,
  declineBusinessType: AcceptNotificationKind.None,
  // as之后不支持使用以下字段，若要使用请调用 getOBAutoMessageList 等相关新接口获取
  submitClientType: AcceptNotificationKind.None,
  acceptClientType: AcceptNotificationKind.None,
  autoMoveClientType: AcceptNotificationKind.None,
  declineClientType: AcceptNotificationKind.None,
  submitTemplate: '',
  acceptTemplate: '',
  autoMoveTemplate: '',
  declineTemplate: '',
  acceptEmailContentTemplate: '',
  acceptEmailSubjectTemplate: '',
  autoMoveEmailContentTemplate: '',
  autoMoveEmailSubjectTemplate: '',
  declineEmailContentTemplate: '',
  declineEmailSubjectTemplate: '',
  submitEmailContentTemplate: '',
  submitEmailSubjectTemplate: '',
  isShowCategories: false,
  // The following fields are simple mappings by DB scheme. DO NOT use them!
  /** @deprecated Use {@link paymentSetting.certainGroupSetting.paymentType} */
  groupPaymentType: 0,
  /** @deprecated Use {@link paymentSetting.certainGroupSetting.prepayType} */
  groupPrepayType: PrepaymentType.FullPay,
  /** @deprecated Use {@link paymentSetting.certainGroupSetting.prepayTipEnable} */
  groupPrepayTipEnable: 0,
  /** @deprecated Use {@link paymentSetting.certainGroupSetting.depositType} */
  groupDepositType: DepositType.ByAmount,
  /** @deprecated Use {@link paymentSetting.certainGroupSetting.depositAmount} */
  groupDepositAmount: 0,
  /** @deprecated Use {@link paymentSetting.certainGroupSetting.depositPercentage} */
  groupDepositPercentage: 0,
  /** @deprecated Use {@link paymentSetting.certainGroupSetting.preAuthPolicy} */
  groupPrepayPolicy: '',
  /** @deprecated Use {@link paymentSetting.certainGroupSetting.preAuthTipEnable} */
  groupPreAuthTipEnable: 0,
  /** @deprecated Use {@link paymentSetting.certainGroupSetting.preAuthPolicy} */
  groupPreAuthPolicy: '',
  /** @deprecated Use {@link paymentSetting.certainGroupSetting.cancellationPolicy} */
  groupCancellationPolicy: '',
  /** @deprecated Use {@link paymentSetting.certainGroupSetting.acceptClient} */
  groupAcceptClient: 0,
  /** @deprecated Use {@link paymentSetting.certainGroupSetting.acceptRule} */
  groupFilterRule: '',
  newClientFlowType: 0,
  tiktok: '',
  availableTimeSync: 0,
}) {
  onlineBookingLink() {
    return appendQuery('/go/', { name: this.bookOnlineName });
  }

  newOnlineBookingLink() {
    return `${URL_ONLINE_BOOKING}/ol/${this.bookOnlineName}/landing`;
  }

  galleryLink() {
    return appendQuery('/go/gallery', { name: this.bookOnlineName });
  }

  get isAvailableBySlot() {
    return this.availableTimeType === AvailableTimeTypeKind.BySlot;
  }

  get isNewOBVersion() {
    return this.useVersion === USE_OB_SETTING_VERSION.NEW_OB;
  }

  get hasSetOldServiceArea() {
    /* 在新service area 上线之前的旧设置，如果对应的值开启了但为空，会被当作是any area */
    return this.isByRadius || this.isByZipcode;
  }

  get hasSetNewServiceArea() {
    /* 从biz select给ob的service area*/
    return this.serviceAreas.length;
  }

  get hasSetServiceArea() {
    return this.hasSetNewServiceArea || this.hasSetOldServiceArea;
  }

  get oldServiceAreaEnable() {
    return this.hasSetOldServiceArea && this.isNeedAddress;
  }

  get newServiceAreaEnable() {
    return this.hasSetNewServiceArea && this.isNeedAddress;
  }

  get certainGroupSettingAcceptRule() {
    const { acceptRule } = this.paymentSetting?.certainGroupSetting || {};

    return acceptRule;
  }
}
