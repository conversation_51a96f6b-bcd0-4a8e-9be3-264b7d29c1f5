/*
 * @since 2020-11-11 11:22:20
 * <AUTHOR> <<EMAIL>>
 */

import { type Option } from '@moego/ui';
import { Record } from 'immutable';
import { type SortOrder } from '../../components/Table/Table.types';
import { type OpenApiDefinitions, type OpenApiModels } from '../../openApi/schema';
import { PagedList } from '../utils/PagedList';
import { createRecordMapBox } from '../utils/RecordMap';
import { createEnum } from '../utils/createEnum';
import { ID_ANONYMOUS } from '../utils/identifier';
import { createBox } from '../utils/utils';
import { OnlineBookingLatestRequestRecord } from './models/OnlineBookingLatestRequest';
import { MostFarAvailableKind, NoShowProtectType, type TNoShowProtectType } from './models/OnlineBookingPreference';
import { OnlineBookingRequestListType, OnlineBookingRequestRecord } from './models/OnlineBookingRequest';

export const enum FullWeekSimpleDay {
  Monday = 'Mon',
  Tuesday = 'Tue',
  Wednesday = 'Wed',
  Thursday = 'Thu',
  Friday = 'Fri',
  Saturday = 'Sat',
  Sunday = 'Sun',
}

export const SortKeyMap = createEnum({
  createTime: [1, 'createdAt'],
  createdAt: [2, 'createdAt'],
  startDate: [3, 'startDate'],
});

export const FullWeekSimpleDayList = [
  FullWeekSimpleDay.Sunday,
  FullWeekSimpleDay.Monday,
  FullWeekSimpleDay.Tuesday,
  FullWeekSimpleDay.Wednesday,
  FullWeekSimpleDay.Thursday,
  FullWeekSimpleDay.Friday,
  FullWeekSimpleDay.Saturday,
];

export const SPECIFIC_DATE_KEY = 'specific-date' as const;

export const MostFarAvailableKindSelectOptions: Option<number | typeof SPECIFIC_DATE_KEY>[] =
  MostFarAvailableKind.values
    .map<Option<number | typeof SPECIFIC_DATE_KEY>>((value) => {
      return {
        label: MostFarAvailableKind.mapLabels[value],
        value,
      };
    })
    .concat({
      label: 'Specific date',
      value: SPECIFIC_DATE_KEY,
    });

export const isMoeGoPayRequiredNoShowProtectType = (type: TNoShowProtectType) => {
  return ([NoShowProtectType.Prepayment, NoShowProtectType.PreAuth] as TNoShowProtectType[]).includes(type);
};

export enum PaymentSettingsAcceptClientType {
  ACCEPT_CLIENT_TYPE_UNSPECIFIED = 0,
  // only accept new client
  ACCEPT_CLIENT_TYPE_NEW = 1,
  // only accept existing client
  ACCEPT_CLIENT_TYPE_EXISTING = 2,
  // accept both new and existing client
  ACCEPT_CLIENT_TYPE_BOTH = 3,
}

export const isNewClientAccepted = (enumValue: PaymentSettingsAcceptClientType) => {
  return !!(enumValue & 1);
};

export const isExistingClientAccepted = (enumValue: PaymentSettingsAcceptClientType) => {
  return !!((enumValue >> 1) & 1);
};

export const BookOnlineStatusMap = createEnum({
  // 已经成为 Appointment
  Appointment: [0, ''],
  // ob 的 booking request (待schedule 后方可成为appointment)
  OnlineBookingRequest: [1, ''],
});

class OnlineBookPaymentSupportAcceptClientRecord extends Record<{
  acceptCustomerType: OpenApiDefinitions['grooming']['com.moego.server.grooming.web.dto.ob.SettingInfoDto']['acceptCustomerType'];
  businessId: number;
}>({
  acceptCustomerType: {
    boarding: 0,
    daycare: 0,
    grooming: 0,
  },
  businessId: ID_ANONYMOUS,
}) {}

export const onlineBookPaymentSupportAcceptClientMapBox = createRecordMapBox(
  'onlineBooking/payment/acceptClientType',
  new OnlineBookPaymentSupportAcceptClientRecord(),
  'businessId',
);

export const onlineBookingRequestMapBox = createRecordMapBox(
  'onlineBooking/requests',
  OnlineBookingRequestRecord,
  'groomingId',
);

export const onlineBookingLatestRequestMapBox = createRecordMapBox(
  'onlineBooking/requests/latest',
  OnlineBookingLatestRequestRecord,
  'apptId',
);

export type OnlineBookingRequestListFilterModel = OpenApiModels['GET/grooming/bookOnline/appointment']['Req'];

export class OnlineBookingRequestListFilterRecord extends Record<OnlineBookingRequestListFilterModel>({
  orderBy: 'createTime',
  orderType: 'desc',
  pageNum: 0,
  pageSize: 0,
  type: OnlineBookingRequestListType.Requests,
}) {
  static ownKey(type: number, businessId: number) {
    return `${businessId}-${type}`;
  }

  order(field: 'createTime' | 'appointmentDate') {
    return this.orderBy === field ? (this.orderType as SortOrder) : void 0;
  }
}

export const businessOnlineBookingRequestListBox = createRecordMapBox(
  'onlineBooking/requests/business',
  new PagedList({ filter: new OnlineBookingRequestListFilterRecord(), key: '' }, 0),
  'key',
);

export const OnlineBookingStaffAvailableType = createEnum({
  Enable: [1, 'Enable'],
  Disable: [0, 'Disable'],
});

export interface OnlineBookingStaffAvailableConfig {
  staffId: number;
  byWorkingHourEnable: number;
  bySlotEnable: number;
  ownId: string;
}

export class OnlineBookingStaffAvailableConfigRecord extends Record<OnlineBookingStaffAvailableConfig>({
  staffId: ID_ANONYMOUS,
  byWorkingHourEnable: OnlineBookingStaffAvailableType.Disable,
  bySlotEnable: OnlineBookingStaffAvailableType.Disable,
  ownId: '',
}) {
  static ownId(businessId: number, staffId: number) {
    return `${businessId}-${staffId}`;
  }

  get enableByWorkingHour() {
    return this.byWorkingHourEnable === OnlineBookingStaffAvailableType.Enable;
  }

  get enableBySlot() {
    return this.bySlotEnable === OnlineBookingStaffAvailableType.Enable;
  }

  get enableTimeSchedule() {
    return this.enableBySlot || this.enableByWorkingHour;
  }
}

export const onlineBookingStaffAvailableConfigMapBox = createRecordMapBox(
  'onlineBooking/staff/available/config',
  OnlineBookingStaffAvailableConfigRecord,
  'ownId',
);

export const OnlineBookingRequestSource = createEnum({
  Google: ['RESERVE_WITH_GOOGLE', 'Google'],
  PetParent: ['PET_PARENT_APP', 'Pet parent portal'],
});

export const onlineBookingNameListBox = createBox<Array<{ businessId: number; urlDomainName: string }>>(
  'onlineBooking/name/list',
  [],
);
