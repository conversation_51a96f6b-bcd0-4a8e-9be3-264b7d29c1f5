import { generateCardFromSlot, getSmartSchedulingSlotList } from '../private/smartSchedulingStore.actions';

import { action } from 'amos';
import { type Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import { DATE_FORMAT_EXCHANGE } from '../../../../utils/DateTimeUtil';
import { ADDING_CARD_ID, smartSchedulingDataStore, smartSchedulingModalStore } from '../../smartSchedulingStore.boxes';
import { selectSmartSchedulingParams } from '../../smartSchedulingStore.selectors';
import { resetSmartSchedulingData, resetSmartSchedulingModal } from '../private/smartSchedulingStore.actions';
import { ID_ANONYMOUS } from '../../../utils/identifier';

export const changeToSpecificDay = action(async (dispatch, select, date: Dayjs) => {
  const modalState = select(smartSchedulingModalStore);
  const smartSchedulingParams = select(selectSmartSchedulingParams());
  let smartSchedulingData = select(smartSchedulingDataStore);
  let smartSchedulingSlotData = smartSchedulingData.data;
  // 如果日历月第一天(或者 startDate) 与日历月最后一天存在数据
  const weekDayOfMonthFirstDay = Number(date.startOf('month').format('d'));
  const calendarMonthFirstDay = date.startOf('month').subtract(weekDayOfMonthFirstDay, 'd');
  let startDate: Dayjs;
  if (!smartSchedulingParams?.startDate) {
    startDate = calendarMonthFirstDay;
  } else {
    startDate = smartSchedulingParams.startDate.isAfter(calendarMonthFirstDay)
      ? smartSchedulingParams.startDate
      : calendarMonthFirstDay;
  }
  if (
    !smartSchedulingSlotData[startDate.format(DATE_FORMAT_EXCHANGE)] ||
    !smartSchedulingSlotData[startDate.add(41, 'd').format(DATE_FORMAT_EXCHANGE)]
  ) {
    // 重新搜索当月可用 slot
    dispatch(smartSchedulingDataStore.merge({ isFetchingData: true }));
    try {
      await dispatch(
        getSmartSchedulingSlotList({
          farthestDay: 42,
          count: 42,
          date: startDate.format(DATE_FORMAT_EXCHANGE),
          filterGroomingId:
            modalState.addingCard?.id !== ADDING_CARD_ID ? Number(modalState.addingCard?.id) : undefined,
        }),
      );
      dispatch(smartSchedulingDataStore.merge({ isFetchingData: false }));
    } catch (error) {
      console.error('Fetch Error in smart scheduling', error);
      dispatch(smartSchedulingDataStore.merge({ isFetchingData: false }));
    }
  }

  // 重新获取一次数据
  smartSchedulingData = select(smartSchedulingDataStore);
  smartSchedulingSlotData = smartSchedulingData.data;
  const { staffIdList, addingCard } = smartSchedulingData;

  const isSingleStaff = staffIdList?.length === 1;
  const formattedDate = date.format(DATE_FORMAT_EXCHANGE);
  const firstAvailableSlotData = smartSchedulingSlotData[formattedDate]?.[0];
  const nextStaffId = isSingleStaff
    ? staffIdList![0]
    : (firstAvailableSlotData?.staffId ?? addingCard?.extendedProps.staffId ?? ID_ANONYMOUS);

  const { availableRange } = smartSchedulingSlotData[formattedDate]?.find((s) => s.staffId === nextStaffId) || {};
  const slot = availableRange?.[0];

  const nextAddingCard = dispatch(
    generateCardFromSlot(addingCard!, { date: date.format(DATE_FORMAT_EXCHANGE), staffId: nextStaffId }, slot),
  );
  dispatch(smartSchedulingDataStore.merge({ addingCard: nextAddingCard }));
});
export const changeStartTimeOfAddingCard = action(
  (dispatch, select, startTime: number, staffId: number | null | undefined) => {
    const smartSchedulingData = select(smartSchedulingDataStore);
    const addingCard = smartSchedulingData.addingCard!;
    const nextStaffId = staffId ? staffId : addingCard.extendedProps.staffId!;
    const date = addingCard.extendedProps.appointmentDate;
    const { availableRange } = smartSchedulingData.getSlotInfo(dayjs(date), nextStaffId) || {};
    const slot = availableRange?.find((s) => s.startTime <= startTime && s.endTime >= startTime);
    const nextAddingCard = dispatch(
      generateCardFromSlot(
        addingCard,
        {
          date: addingCard.extendedProps.appointmentDate,
          staffId: nextStaffId,
          startTime,
        },
        slot,
      ),
    );
    dispatch(smartSchedulingDataStore.merge({ addingCard: nextAddingCard }));
  },
);
export const exitSmartScheduling = action((dispatch) => {
  dispatch([resetSmartSchedulingModal(), resetSmartSchedulingData()]);
});
