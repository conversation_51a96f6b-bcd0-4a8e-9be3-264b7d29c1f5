/*
 * @since 2020-08-19 11:09:52
 * <AUTHOR> <<EMAIL>>
 */

import { type QueryStaffListByPaginationParams } from '@moego/api-web/moego/api/organization/v1/staff_api';
import { StaffLinkStatus } from '@moego/api-web/moego/models/organization/v1/staff_enums';
import { Record } from 'immutable';
import { type OpenApiModels } from '../../openApi/schema';
import { getStaffColorCode } from '../../utils/utils';
import { OwnList, createOwnListBox } from '../utils/OwnList';
import { PagedList } from '../utils/PagedList';
import { createRecordMapBox } from '../utils/RecordMap';
import { createEnum } from '../utils/createEnum';
import { ID_ANONYMOUS, ID_LOADING } from '../utils/identifier';
import { createBox } from '../utils/utils';
import { type LimitationFormValue } from '../../container/settings/Settings/StaffSetting/ShiftManagement/components/Limitation/LimitationFormContext';

export class StaffNotificationRecord extends Record({
  staffId: 0,
  bookingCreated: 0,
  bookingCancelled: 0,
  bookingRescheduled: 0,
  newBooking: 0,
  newIntakeForm: 0,
  agreementSigned: 0,
  invoicePaid: 0,
  reviewSubmitted: 0,
  newAbandonedBookings: 0,
}) {}

export const StaffKinds = createEnum({
  EnterpriseOwner: [2, 'Enterprise Owner'],
  Owner: [1, 'Owner'],
  Staff: [0, 'Staff'],
  EnterpriseStaff: [3, 'Enterprise Staff'],
});

export const StaffPayBy = createEnum({
  Commission: [0, 'Commission based'],
  Hourly: [1, 'Hourly based'],
});

export class StaffRecord extends Record({
  id: ID_ANONYMOUS,
  businessId: 0,
  accountId: 0,
  roleId: 0,
  avatarPath: '',
  firstName: '',
  lastName: '',
  employeeCategory: StaffKinds.Staff,
  phoneNumber: '',
  hireDate: 0,
  fireDate: 0,
  allowLogin: 0,
  groupLeaderId: 0,
  note: '',
  inactive: 0,
  status: 0,
  createById: 0,
  sort: 0,
  bookOnlineAvailable: 0,
  createTime: 0,
  /**
   * @deprecated use `isShowOnCalendar` instead
   */
  showOnCalendar: 0,
  isShowOnCalendar: false,
  showCalendarStaffAll: 0,
  /**
   * 注意：这个 email 字段在 AS 后，后台没有直接返回，需要前端手动取
   * 如果要依赖，得看下写入这个 box 的 action 是否有覆盖到
   *
   * 目前已知可以取到的是：getStaffFullDetail, queryStaffListByPagination 的 staffEmail.email 字段
   * 使用广泛的 getCompanyStaffList 的返回没有带上这个
   **/
  email: '',
  inviteCode: '',
  inviteLinkRecipientEmail: '',
  accessStaffIdList: [] as number[],
  accessCode: '', // Clock in/out access code
  accessCodeCopy: '',
  payBy: StaffPayBy.Commission,
  servicePayRate: 0,
  addonPayRate: 0,
  hourlyPayRate: 0,
  tipsPayRate: 0,
  linkStatus: StaffLinkStatus.UNSPECIFIED,
  colorCode: '',
}) {
  getColorCode() {
    return getStaffColorCode(this.colorCode, this.firstName, this.lastName);
  }

  fullName() {
    return this.firstName + ' ' + this.lastName;
  }

  isOwner() {
    return StaffKinds.Owner === this.employeeCategory || StaffKinds.EnterpriseOwner === this.employeeCategory;
  }

  isCompanyOwner() {
    return StaffKinds.Owner === this.employeeCategory;
  }

  isEnterpriseOwner() {
    return StaffKinds.EnterpriseOwner === this.employeeCategory;
  }

  isEnterpriseStaff() {
    return StaffKinds.EnterpriseStaff === this.employeeCategory;
  }

  isEnterpriseRelatedStaff() {
    return this.isEnterpriseOwner() || this.isEnterpriseStaff();
  }

  isStaff() {
    return StaffKinds.Staff === this.employeeCategory;
  }
}

export interface WorkingHourTimeRange {
  startTime: number; // minutes
  endTime: number;
}

export interface WorkingHourValue {
  isAvailable: boolean;
  timeRange: WorkingHourTimeRange[];
  limit: LimitationFormValue;
}

export interface WorkingSlotValue {
  isAvailable: boolean;
  slotDailySetting: {
    startTime: number;
    endTime: number;
    capacity: number;
    limit: LimitationFormValue;
  };
  slotHourSettingList: {
    startTime: number;
    capacity: number;
    limit: LimitationFormValue;
  }[];
}

export interface ServiceAreaRange {
  areaId: number;
}

export class WorkingHourRecord extends Record({
  id: 0,
  businessId: 0,
  staffId: 0,
  date: '',
  createTime: 0,
  updateTime: 0,
  dayOfWeek: 0,
  timeRange: [] as WorkingHourTimeRange[],
  isRepeat: 0,
  _ownId: '',
}) {
  static ownId(staffId: number, dayOfWeek: number) {
    return `${staffId}-${dayOfWeek}`;
  }
}

export const StaffCategory = createEnum({
  Employee: [0, 'Staff'],
  Employer: [1, 'Owner'],
});

export const StaffPayrollServiceCommissionType = createEnum({
  FixedRate: [1, 'By fixed rate'],
  TierRate: [2, 'By tiered rate'],
});

export const StaffPayrollServiceCommissionActionType = createEnum({
  FixedRate: [1, 'selectFixedRate'],
  TierRate: [2, 'selectTieredRate'],
});

export const StaffPayrollTierType = createEnum({
  SlidingScale: [1, 'Sliding scale'],
  Progressive: [2, 'Progressive'],
});

type StaffPayrollSettingModel = OpenApiModels['GET/business/payroll/setting/staff/list']['Res'][number];
export class StaffPayrollSettingRecord extends Record<StaffPayrollSettingModel>({
  id: ID_ANONYMOUS,
  staffId: ID_ANONYMOUS,
  // The config of commission by service
  serviceCommissionEnable: false,
  serviceCommissionType: StaffPayrollServiceCommissionType.FixedRate,
  servicePayRate: 0,
  addonPayRate: 0,
  tierType: StaffPayrollTierType.SlidingScale,
  tierConfig: [],

  // The config of commission by hourly
  hourlyCommissionEnable: false,
  hourlyPay: 0,

  // The config of tip
  tipsCommissionEnable: false,
  tipsPayRate: 0,

  // others
  createTime: 0,
  updateTime: 0,
  businessId: ID_ANONYMOUS,
}) {}

export class AccountPagedStaffListFilterRecord extends Record<
  Pick<QueryStaffListByPaginationParams, 'businessIds'> & { type: 'single' | 'all' }
>({
  businessIds: [],
  type: 'all',
}) {}

export const staffMapBox = createRecordMapBox('staffs', StaffRecord, 'id');
export const currentStaffIdBox = createBox('staffs/current', ID_LOADING);
export const businessStaffListBox = createOwnListBox('staffs/business', OwnList.nn());
export const accountStaffListBox = createOwnListBox('staffs/account', OwnList.nn());

export const staffWorkingHourMapBox = createRecordMapBox('staffs/working_hours', WorkingHourRecord, '_ownId');

export const staffNotificationMapBox = createRecordMapBox('staffs/notifications', StaffNotificationRecord, 'staffId');
export const staffPayrollSettingMapBox = createRecordMapBox(
  'staffs/payroll_setting',
  StaffPayrollSettingRecord,
  'staffId',
);

export const accountPagedStaffListBox = createRecordMapBox(
  'staffs/account/paged',
  new PagedList({ filter: new AccountPagedStaffListFilterRecord(), key: 0 }, 0),
  'key',
);

export const companyStaffListBox = createOwnListBox('staffs/company', OwnList.nn());
