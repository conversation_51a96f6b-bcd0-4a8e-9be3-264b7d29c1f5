import { DateType, ServiceType } from '@moego/api-web/moego/models/offering/v1/service_enum';
import { Text, cn } from '@moego/ui';
import { useSelector } from 'amos';
import dayjs from 'dayjs';
import React from 'react';
import { ComMoegoServerGroomingDtoPrintcardStayCardAppointmentMainServiceItemType as MainServiceItemType } from '../../../../openApi/grooming-schema';
import type { BusinessRecord } from '../../../../store/business/business.boxes';
import { selectCurrentBusiness } from '../../../../store/business/business.selectors';
import { type StayPrintCardInfo } from '../../../../store/printCard/stayCard/stayCard.actions';
import { useExtraServicesAndAddOnsCheckTable } from '../../hooks/useExtraServicesAndAddOnsCheckTable';
import { CheckTableGroup } from '../StayCard/CheckTables';
import { DateTypeWordingMap } from '../../../DateType/DateType.utils';
import { Condition } from '../../../Condition';

export type ExtraServiceDetails = StayPrintCardInfo['extraServiceDetails'];

interface StayCardAddOnsContentProps {
  extraServiceDetails: ExtraServiceDetails;
  mainServiceItemType?: MainServiceItemType;
  showScheduleTable?: boolean;
}

interface GetActualDatesStringParams {
  actualDates: string[];
  dateType: DateType;
  staffName: string;
  startTime: string | number;
}

const AddOnItem = ({
  extraServiceDetail,
  mainServiceItemType,
  showScheduleTable,
  business,
}: {
  extraServiceDetail: ExtraServiceDetails[0];
  mainServiceItemType?: MainServiceItemType;
  showScheduleTable?: boolean;
  business: BusinessRecord;
}) => {
  const {
    serviceName,
    serviceType,
    requireDedicatedStaff,
    quantityPerDay,
    actualDates,
    staffName,
    startTime,
    dateType,
  } = extraServiceDetail || {};

  const { dataGroup, columnsGroup } = useExtraServicesAndAddOnsCheckTable({
    actualDates: actualDates?.sort((a, b) => (dayjs(a).isBefore(dayjs(b)) ? -1 : 1)) || [],
    quantityPerDay: quantityPerDay || 0,
  });

  const formatActualDate = (actualDate: string | number) => dayjs(actualDate).format(business.dateFormatMD);
  const formatStartTime = (startTime: string | number) => {
    // 这里后端是 int64 定义，但是实际上只会传 number 过来
    const startDayTime = dayjs().setMinutes(+startTime || 0);
    return business.formatTime(startDayTime);
  };
  const getSpecificTimeStrWithStaffName = (startTime: string | number, staffName: string, prefix = '') => {
    return staffName ? `${prefix}${formatStartTime(startTime)}, by ${staffName}` : '';
  };

  const getActualDatesString = (params: GetActualDatesStringParams) => {
    const { dateType, actualDates, staffName, startTime } = params;
    if ([DateType.SPECIFIC_DATE, DateType.DATE_POINT].includes(dateType)) {
      return actualDates
        .sort((a, b) => (dayjs(a).isBefore(dayjs(b)) ? -1 : 1))
        .map((date) => {
          // showScheduleTable 为 true 时，不显示 formatSpecificDate
          if (showScheduleTable) {
            return `${getSpecificTimeStrWithStaffName(startTime, staffName)}`;
          }
          return `${formatActualDate(date)}${getSpecificTimeStrWithStaffName(startTime, staffName, ', ')}`;
        })
        .filter(Boolean)
        .join(', ');
    }
    const dateTypeLabel = DateTypeWordingMap[dateType] ?? '';
    return `${dateTypeLabel.toLocaleLowerCase()}${getSpecificTimeStrWithStaffName(startTime, staffName, ', ')}`;
  };

  if (mainServiceItemType !== MainServiceItemType.BOARDING) {
    return (
      <div className="moe-mt-xs">
        <Text variant="small" className={cn('moe-text-[12px]', { 'moe-mb-xs': showScheduleTable })}>
          <Text variant="small" as="span" className="moe-text-[12px] moe-font-bold">
            {serviceName || '-'}
          </Text>
          <Text variant="small" as="span" className="moe-text-[12px] moe-ml-xxs">
            {requireDedicatedStaff
              ? `(${getSpecificTimeStrWithStaffName(startTime, staffName)})`
              : `(x${quantityPerDay})`}
          </Text>
        </Text>
        {showScheduleTable && <CheckTableGroup columnsGroup={columnsGroup} dataGroup={dataGroup} />}
      </div>
    );
  }

  const boardingQuantityText =
    serviceType === ServiceType.ADDON && !requireDedicatedStaff ? `x${quantityPerDay} per day` : '';
  const boardingActualDatesString = getActualDatesString({
    dateType,
    actualDates,
    staffName,
    startTime,
  });

  return (
    <div className="moe-mt-xs">
      <div className={cn('moe-flex moe-gap-x-xxs moe-text-primary', { 'moe-mb-xs': showScheduleTable })}>
        <Text variant="small" className="moe-max-w-[160px] moe-text-wrap moe-text-[12px] moe-font-bold">
          {serviceName || '-'}
        </Text>
        <Condition if={boardingQuantityText || boardingActualDatesString}>
          <Text variant="small" className="moe-flex-1 moe-text-[12px]">
            ({[boardingQuantityText, boardingActualDatesString].filter(Boolean).join(', ')})
          </Text>
        </Condition>
      </div>
      {showScheduleTable && <CheckTableGroup columnsGroup={columnsGroup} dataGroup={dataGroup} />}
    </div>
  );
};

export const StayCardAddOnsContent: React.FC<StayCardAddOnsContentProps> = ({
  extraServiceDetails,
  mainServiceItemType,
  showScheduleTable,
}) => {
  const [business] = useSelector(selectCurrentBusiness());
  if (!extraServiceDetails.length) {
    return (
      <Text variant="small" className="moe-text-[12px]">
        No extra services & add-ons available
      </Text>
    );
  }

  if (mainServiceItemType !== MainServiceItemType.BOARDING) {
    return (
      <>
        {extraServiceDetails.map((item, index) => (
          <AddOnItem
            key={index}
            extraServiceDetail={item}
            mainServiceItemType={mainServiceItemType}
            showScheduleTable={showScheduleTable}
            business={business}
          />
        ))}
      </>
    );
  }

  return (
    <div className="moe-flex moe-flex-col moe-gap-y-xxs moe-mt-xxs">
      {extraServiceDetails.map((item, index) => {
        return (
          <AddOnItem
            key={index}
            extraServiceDetail={item}
            mainServiceItemType={mainServiceItemType}
            showScheduleTable={showScheduleTable}
            business={business}
          />
        );
      })}
    </div>
  );
};
