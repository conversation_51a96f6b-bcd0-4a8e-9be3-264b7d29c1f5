import { useSelector } from 'amos';
import dayjs from 'dayjs';
import chunk from 'lodash/chunk';
import React, { useMemo } from 'react';

import { selectCurrentBusiness } from '../../../store/business/business.selectors';

const MAX_ITEM_COUNT_ONE_ROW = 7;

export const useExtraServicesAndAddOnsCheckTable = ({
  actualDates,
  quantityPerDay,
}: {
  actualDates: string[];
  quantityPerDay: number;
}) => {
  const [business] = useSelector(selectCurrentBusiness());
  const columns = actualDates.map((actualDate) => {
    const day = dayjs(actualDate);
    const weekday = day.format('ddd');
    const formattedDay = day.format(business.dateFormatMD);

    return {
      title: (
        <div className="moe-flex moe-flex-col moe-items-center moe-justify-center moe-py-spacing-xxs">
          <span>
            {weekday} {formattedDay}
          </span>
        </div>
      ),
      dataIndex: formattedDay,
      className: 'moe-min-w-[80px]',
    };
  });

  const columnsGroup = useMemo(
    () =>
      chunk(columns, MAX_ITEM_COUNT_ONE_ROW).map((item) => {
        return item;
      }),
    [columns],
  );

  const dataGroup = columnsGroup.map((item) =>
    Array.from({ length: quantityPerDay }).map(() => {
      return {
        ...Object.fromEntries(item.map(({ dataIndex }) => [dataIndex, ''])),
      };
    }),
  );

  return {
    dataGroup,
    columnsGroup,
  };
};
