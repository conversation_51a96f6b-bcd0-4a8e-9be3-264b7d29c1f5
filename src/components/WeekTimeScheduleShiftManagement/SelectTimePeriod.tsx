import { TimeFormat } from '@moego/api-web/moego/models/organization/v1/company_enums';
import { MinorPlusOutlined, MinorTrashOutlined } from '@moego/icons-react';
import { IconButton, type IconButtonProps, TimeRangePicker, cn } from '@moego/ui';
import { useSelector } from 'amos';
import React from 'react';
import { selectCurrentBusiness } from '../../store/business/business.selectors';
import { useBool } from '../../utils/hooks/useBool';
import { useControllableValue } from '../../utils/hooks/useControlledValue';
import { dayjsToMinutes, minutesToDayjs } from '../../utils/utils';
import type { TimePeriod } from './types';

export interface SelectTimePeriodProps {
  className?: string;
  disabled?: boolean;
  value?: TimePeriod;
  minuteStep?: number;
  onChange?: (range: TimePeriod) => void;
  startDisabled?: (value: number) => boolean;
  endDisabled?: (value: number) => boolean;
  deletable?: boolean;
  addable?: boolean;
  onDelete?: () => void;
  onAdd?: () => void;
  buttonSize?: IconButtonProps['size'];
  buttonProps?: IconButtonProps;
  buttonClassName?: string;
  timeRangePickerClassName?: string;
  /** 展示在 time range picker 和按钮的中间 */
  middleContent?: React.ReactNode;
}

export function SelectTimePeriod(props: SelectTimePeriodProps) {
  const {
    className,
    deletable,
    startDisabled,
    endDisabled,
    onDelete,
    addable,
    onAdd,
    disabled,
    minuteStep,
    buttonSize = 's',
    buttonProps,
    buttonClassName,
    middleContent,
    timeRangePickerClassName,
  } = props;
  const [value, setValue] = useControllableValue<TimePeriod>(props, {
    defaultValue: { startTime: 0, endTime: 60 },
  });
  const [business] = useSelector(selectCurrentBusiness);
  const isOpen = useBool();

  const renderActions = () => {
    if (disabled) return null;

    return (
      <div
        className={cn('moe-absolute moe-right-[-12px] moe-gap-x-[12px] moe-flex moe-translate-x-full', buttonClassName)}
      >
        {addable && <IconButton icon={<MinorPlusOutlined />} onPress={onAdd} size={buttonSize} {...buttonProps} />}
        {deletable && (
          <IconButton icon={<MinorTrashOutlined />} onPress={onDelete} size={buttonSize} {...buttonProps} />
        )}
      </div>
    );
  };

  return (
    <div className={cn('moe-flex moe-items-center moe-gap-x-[24px]', className)}>
      <TimeRangePicker
        minuteStep={minuteStep}
        value={[minutesToDayjs(value.startTime), minutesToDayjs(value.endTime)]}
        onOpenChange={(v) => {
          isOpen.as(v);
        }}
        isOpen={isOpen.value}
        startDisabledTime={(date) => {
          const mins = dayjsToMinutes(date);
          return startDisabled ? startDisabled(mins) : false;
        }}
        endDisabledTime={(date) => {
          const mins = dayjsToMinutes(date);
          return endDisabled ? endDisabled(mins) : false;
        }}
        isClearable={false}
        onChange={(v) => {
          if (!v || !v[0] || !v[1]) return;
          isOpen.close();
          setValue({
            startTime: dayjsToMinutes(v[0]),
            endTime: dayjsToMinutes(v[1]),
          });
        }}
        className={cn('moe-flex-1 moe-min-w-[234px]', timeRangePickerClassName)}
        use12Hours={business.timeFormatType === TimeFormat.HOUR_12}
        isDisabled={disabled}
      />
      {middleContent}
      {renderActions()}
    </div>
  );
}
