export enum SMTestIds {
  SettingAvailabilityType = 'sm-set-availability-type-rdo',
  SettingScheduleTypeSelect = 'sm-set-schedule-type-select',
  SettingUpdateAvailabilityBtn = 'sm-set-update-availability-btn',
  SettingCancelAvailabilityBtn = 'sm-set-cancel-availability-btn',
  SettingViewTypeSwitch = 'sm-set-view-type-switch',
  SettingStaffScheduleStaffMenu = 'sm-set-staff-schedule-staff-menu',

  // date override
  SettingDateOverrideList = 'sm-set-date-override-list',
  SettingDateOverrideTypeDropdown = 'sm-set-date-override-type-dropdown-menu',
  SettingDateOverrideAddBtn = 'sm-set-date-override-add-btn',

  // date override drawer
  SettingDateOverrideDrawerCalendar = 'sm-set-date-override-drawer-calendar',
  SettingDateOverrideDrawerCalendarExpandBtn = 'sm-set-date-override-drawer-calendar-expand-btn',

  // date override drawer by slot
  SettingDateOverrideDrawerWorkingOnCb = 'sm-set-date-override-drawer-working-on-cb',
  SettingDateOverrideDrawerWorkingHourDatePickerBySlot = 'sm-set-date-override-drawer-date-picker-by-slot',
  SettingDateOverrideDrawerDailyPetCapacityInput = 'sm-set-date-override-drawer-daily-pet-capacity-input',
  SettingDateOverrideDrawerSlotDatePicker = 'sm-set-date-override-drawer-slot-date-picker',
  SettingDateOverrideDrawerSlotPetCapacityInput = 'sm-set-date-override-drawer-slot-pet-capacity-input',

  // date override drawer by time
  SettingDateOverrideDrawerWorkingHourDatePickerByTime = 'sm-set-date-override-drawer-date-picker-by-time',
  SettingDateOverrideDrawerWorkingHourTimeRangeAddBtn = 'sm-set-date-override-drawer-time-range-add-btn',
  SettingDateOverrideDrawerWorkingHourTimeRangeDeleteBtn = 'sm-set-date-override-drawer-time-range-delete-btn',
}
