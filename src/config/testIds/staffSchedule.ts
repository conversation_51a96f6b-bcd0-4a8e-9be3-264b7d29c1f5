export enum StaffScheduleTestIds {
  // by slot
  SlotScheduleWeekList = 'ss-slot-week-list',
  SlotScheduleDailySettingRow = 'ss-daily-setting-row',
  SlotScheduleDailyHourTimePicker = 'ss-daily-hour-time-picker',
  SlotScheduleDailyPetCapacityInput = 'daily-pet-setting-capacity-input',
  SlotScheduleDailyCopyBtn = 'ss-daily-copy-btn',
  SlotScheduleDailyEditBtn = 'ss-daily-edit-btn',

  SlotScheduleSlotListItem = 'ss-slot-list-item',
  SlotScheduleSlotListItemPetCapacityInput = 'slot-list-pet-capacity-input',
  SlotScheduleSlotListItemEditBtn = 'ss-slot-list-item-edit-btn',
  SlotScheduleSlotListItemDeleteBtn = 'ss-slot-list-item-delete-btn',

  LimitationRow = 'ss-limitation-row',

  // by time
  WorkingHourScheduleTime = 'ss-working-hour-schedule-time',

  // service limitation
  ServiceLimitationCheckbox = 'ss-service-limitation-checkbox',
  ServiceLimitationServiceSelector = 'ss-service-limitation-service-selector',
  ServiceLimitationPetCountInput = 'ss-service-limitation-pet-count-input',
  ServiceLimitationDeleteBtn = 'ss-service-limitation-delete-btn',

  // pet size limitation
  PetSizeLimitationCheckbox = 'ss-pet-size-limitation-checkbox',
  PetSizeLimitationPetSizeSelector = 'ss-pet-size-limitation-pet-size-selector',
  PetSizeLimitationPetCountInput = 'ss-pet-size-limitation-pet-size-input',
  PetSizeLimitationDeleteBtn = 'ss-pet-size-limitation-delete-btn',

  // pet breed limitation
  PetBreedLimitationCheckbox = 'ss-pet-breed-limitation-checkbox',
  PetBreedLimitationPetTypeSelector = 'ss-pet-breed-limitation-pet-type-selector',
  PetBreedLimitationPetBreedSelector = 'ss-pet-breed-limitation-pet-breed-selector',
  PetBreedLimitationPetCountInput = 'ss-pet-breed-limitation-pet-count-input',
  PetBreedLimitationDeleteBtn = 'ss-pet-breed-limitation-delete-btn',
}
