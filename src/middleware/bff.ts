import {
  createFileClient,
  createLeadsClient,
  createOrderClient,
  createBookClient,
  createOpenPlatformClient,
  createSalesClient,
} from '@moego/bff-openapi';
import { http } from './api';

export const BFFFileClient = createFileClient(http.bff);

// for test
export const BFFBookClient = createBookClient(http.bff);

export const BffLeadsClient = createLeadsClient(http.bff, {
  onValidateRequestError() {
    return true;
  },
  onValidateResponseError() {
    return true;
  },
});

export const BFFOrderClient = createOrderClient(http.bff, {
  // TODO（Sam）：等待刀子处理完 zod 校验再放开。
  onValidateResponseError: () => {
    return true;
  },
});

export const BFFOpenPlatformClient = createOpenPlatformClient(http.bff, {
  onValidateResponseError: () => {
    return true;
  },
  onValidateRequestError: () => {
    return true;
  },
});

export const BFFSalesClient = createSalesClient(http.bff, {
  onValidateResponseError: () => {
    return true;
  },
  onValidateRequestError: () => {
    return true;
  },
});
