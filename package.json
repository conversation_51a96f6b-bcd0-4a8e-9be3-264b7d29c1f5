{"name": "boarding", "version": "0.1.0", "private": true, "scripts": {"dev": "rsbuild dev", "dev:fast": "FAST_DEV=1 pnpm run dev", "build": "rsbuild build", "preview": "pnpm build && rsbuild preview", "doctor": "RSDOCTOR=true rsbuild build", "check:deps": "dpdm -T --no-tree --no-warning --exit-code circular:1 src/index.tsx  --exclude '/(node_modules|src/container/PaymentFlow)/'", "check:ts-ignore": "if git --no-pager grep -n -i @ts''-ignore; then echo '\\033[31m@ts''-ignore is forbidden, please fix it.\\033[0m'; exit 1; fi", "check:types": "tsc --noEmit", "check:useless": "npx unimported", "check:biome": "pnpm biome check --diagnostic-level error", "check:text-lint": "NODE_OPTIONS=--no-deprecation node ./scripts/extract_text.mjs", "generate": "node ./scripts/build_icons.js", "preinstall": "npx only-allow pnpm", "prepare": "husky install", "lint-push": "bash ./ci/lint.sh && git push -u origin $(git rev-parse --abbrev-ref HEAD)", "openapi": "openapi2dts --outputRest src/openApi/", "tinify": "node ./scripts/tinify.js", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "lint-staged": {"*.{md,ts,tsx,js,jsx,css,less,scss,html,json,yaml}": "pnpm biome check --write --no-errors-on-unmatched", "*.tsx": "pnpm check:text-lint", "*.{ts,tsx,js,jsx}": "pnpm eslint --cache --fix --quiet"}, "browserslist": ["chrome >= 67", "edge >= 79", "firefox >= 68", "safari >= 14"], "dependencies": {"@ant-design/icons": "^4.3.0", "@dagrejs/dagre": "^1.1.4", "@datadog/browser-rum": "^5.35.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "^1.1.2", "@emoji-mart/react": "^1.1.1", "@fullcalendar/common": "5.9.0", "@fullcalendar/daygrid": "5.9.0", "@fullcalendar/interaction": "5.9.0", "@fullcalendar/list": "5.9.0", "@fullcalendar/moment": "5.9.0", "@fullcalendar/react": "5.9.0", "@fullcalendar/resource-common": "5.9.0", "@fullcalendar/resource-daygrid": "5.9.0", "@fullcalendar/resource-timegrid": "5.9.0", "@fullcalendar/resource-timeline": "5.9.0", "@fullcalendar/scrollgrid": "5.9.0", "@fullcalendar/timegrid": "5.9.0", "@googlemaps/js-api-loader": "^1.15.1", "@googlemaps/markerclusterer": "^2.5.0", "@googlemaps/markerwithlabel": "^2.0.25", "@growthbook/growthbook-react": "^1.0.1", "@kanmon/web-sdk": "2.2.0", "@lottiefiles/react-lottie-player": "^3.5.3", "@moego/api-web": "1.***********.1", "@moego/api-web-v2": "1.***********.0", "@moego/bff-openapi": "0.0.90", "@moego/business-components": "1.4.0", "@moego/call-center": "0.200.0", "@moego/finance-assets": "1.0.20", "@moego/finance-plugins": "1.0.20", "@moego/finance-terminal": "1.0.20", "@moego/finance-ui": "1.0.20", "@moego/finance-utils": "1.0.20", "@moego/finance-web-kit": "1.0.20", "@moego/fn-components": "0.207.0", "@moego/http-client": "0.202.0", "@moego/icons-react": "^1.13.0", "@moego/pagespy": "0.11.0", "@moego/reporting": "0.211.0", "@moego/tools": "1.13.0", "@moego/ui": "0.399.0", "@moego/workflow": "0.211.0", "@reach/observe-rect": "^1.2.0", "@react-email/button": "^0.0.9", "@react-email/column": "^0.0.7", "@react-email/container": "^0.0.8", "@react-email/font": "^0.0.2", "@react-email/heading": "^0.0.8", "@react-email/hr": "^0.0.5", "@react-email/img": "^0.0.5", "@react-email/link": "^0.0.5", "@react-email/row": "^0.0.5", "@react-email/section": "^0.0.9", "@react-email/text": "^0.0.5", "@sentry/browser": "8.31.0", "@sourcebug/amos": "^0.2.21", "@sourcebug/image-lib": "^0.0.8", "@square/web-sdk": "^2.0.0", "@stripe/react-stripe-js": "^1.1.2", "@stripe/stripe-js": "^1.9.0", "@stripe/terminal-js": "^0.11.2", "@tanstack/react-query": "^4.40.1", "@tinymce/tinymce-react": "^4.3.0", "@xyflow/react": "^12.3.2", "ahooks": "^3.8.1", "amos": "0.2.11", "amos-devtools": "^0.2.2", "animate.css": "^4.1.1", "antd": "^4.9.2", "axios": "^1.7.2", "big.js": "^6.2.1", "broadcastchannel-polyfill": "^1.0.1", "camelcase": "^6.3.0", "chroma-js": "^3.1.1", "classnames": "^2.3.1", "copy-to-clipboard": "^3.3.3", "country-data-list": "^1.2.1", "date-fns": "^4.1.0", "date-holidays": "^3.21.1", "dayjs": "^1.11.10", "dnd-core": "11.1.3", "echarts": "^5.3.3", "echarts-for-react": "^3.0.1", "emoji-regex": "^10.3.0", "eventemitter3": "^5.0.1", "file-saver": "^2.0.5", "firebase": "^7.21.1", "hash-wasm": "^4.11.0", "heic2any": "^0.0.4", "history": "^4.10.1", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "html5parser": "^2.0.1", "iclipboard": "^1.0.1", "immutability-helper": "^3.1.1", "immutable": "4.1.0", "intercom-client": "^2.11.0", "javascript-time-ago": "^2.0.13", "js-cookie": "^3.0.0", "lodash": "^4.17.20", "md5": "^2.3.0", "monofile-utilities": "^5.0.0", "numeral": "^2.0.6", "ordinal": "^1.0.3", "path-to-regexp": "^6.1.0", "ping.js": "^0.3.0", "polished": "^3.6.7", "private-ip": "^2.2.1", "qr-code-styling": "1.6.0-rc.1", "raf": "^3.4.1", "rc-field-form": "^1.17.2", "rc-menu": "^8.10.1", "rc-notification": "^4.5.4", "rc-picker": "^2.4.3", "rc-resize-observer": "^1.4.0", "rc-select": "^11.5.3", "rc-tabs": "^11.7.2", "rc-tooltip": "^5.0.1", "rc-trigger": "^5.2.0", "rc-util": "^5.5.0", "react": "^17.0.2", "react-app-polyfill": "^3.0.0", "react-collapsed": "^4.1.2", "react-color": "^2.19.3", "react-countup": "^6.5.3", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dom": "^17.0.2", "react-draggable": "^4.4.4", "react-infinite-scroll-component": "^6.1.0", "react-international-phone": "^4.3.0", "react-motion": "^0.5.2", "react-number-format": "^4.6.4", "react-page-visibility": "^6.1.0", "react-payment-inputs": "^1.1.7", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-router-transition": "^2.0.0", "react-signature-canvas": "^1.0.3", "react-to-print": "^2.12.0", "react-transition-group": "^4.4.1", "react-use": "^15.3.4", "react-virtualized-auto-sizer": "^1.0.2", "react-window": "^1.8.6", "react-window-infinite-loader": "^1.0.5", "reselect": "^4.0.0", "scheduler": "^0.20.2", "scroll-into-view-if-needed": "^3.1.0", "spark-md5": "^3.0.2", "stripe": "^8.217.0", "styled-components": "^5.2.0", "supercluster": "^8.0.1", "tailwind-scrollbar-hide": "^2.0.0", "timezones.json": "^1.7.1", "tiny-emitter": "^2.1.0", "use-context-selector": "^2.0.0", "use-deep-compare-effect": "^1.8.1", "userpilot": "^1.2.5", "utility-types": "^3.10.0", "uuid": "^10.0.0", "validator": "^13.5.2", "web-vitals": "^2.1.4", "xlsx": "^0.18.5", "yup": "^1.3.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@commitlint/cli": "^11.0.0", "@commitlint/config-angular": "^11.0.0", "@inquirer/prompts": "^7.0.1", "@moego/eslint-plugin-moego-fe": "^0.205.0", "@moego/openapi2dts": "1.23.0", "@octokit/rest": "^22.0.0", "@rsbuild/core": "1.3.9", "@rsbuild/plugin-basic-ssl": "^1.1.1", "@rsbuild/plugin-less": "1.2.2", "@rsbuild/plugin-react": "1.2.0", "@rsbuild/plugin-sass": "1.3.1", "@rsbuild/plugin-styled-components": "1.3.0", "@rsbuild/plugin-svgr": "1.2.0", "@rsbuild/plugin-type-check": "1.2.1", "@rsdoctor/rspack-plugin": "^0.4.7", "@sentry/types": "^8.31.0", "@sentry/webpack-plugin": "^2.22.6", "@tanstack/eslint-plugin-query": "^5.81.2", "@tanstack/react-virtual": "3.0.0-beta.68", "@testing-library/dom": "^8.17.1", "@testing-library/react": "^12.1.5", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.4.3", "@types/benchmark": "^1.0.33", "@types/big.js": "^6.1.6", "@types/chroma-js": "^2.4.4", "@types/classnames": "^2.2.10", "@types/echarts": "^4.9.22", "@types/file-saver": "^2.0.1", "@types/fs-extra": "^9.0.11", "@types/google.maps": "^3.54.1", "@types/googlemaps": "^3.43.3", "@types/history": "^4.7.8", "@types/html2canvas": "^0.0.35", "@types/intercom-client": "^2.11.8", "@types/intercom-web": "^2.8.11", "@types/javascript-time-ago": "^2.0.1", "@types/js-cookie": "^3.0.2", "@types/lodash": "^4.14.165", "@types/lodash-es": "^4.17.12", "@types/md5": "^2.3.0", "@types/mockjs": "^1.0.3", "@types/node": "^18.19.86", "@types/numeral": "^0.0.28", "@types/private-ip": "^1.0.0", "@types/raf": "^3.4.0", "@types/react": "^17.0.80", "@types/react-color": "^3.0.6", "@types/react-dom": "^17.0.25", "@types/react-motion": "^0.0.29", "@types/react-router": "^5.1.8", "@types/react-router-dom": "^5.1.5", "@types/react-signature-canvas": "^1.0.1", "@types/react-transition-group": "^4.4.0", "@types/react-window": "^1.8.2", "@types/react-window-infinite-loader": "^1.0.3", "@types/recompose": "^0.30.7", "@types/spark-md5": "^3.0.4", "@types/styled-components": "^5.1.3", "@types/supercluster": "^7.1.3", "@types/uuid": "^10.0.0", "@types/validator": "^13.1.2", "@types/webpack-env": "^1.15.3", "@types/yargs": "15.0.14", "@visionm/click-to-react-component": "^1.1.1", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.0.7", "axios-mock-adapter": "^2.0.0", "benchmark": "^2.1.4", "bfj": "^7.0.2", "browserslist": "^4.21.4", "camelcase": "^6.2.1", "cross-env": "^7.0.2", "cspell": "^8.3.2", "deep-diff": "^1.0.2", "diff-effect": "^0.1.4", "dotenv": "9.0.2", "dotenv-expand": "5.1.0", "dpdm-fast": "^1.0.13", "eslint": "^9.22.0", "fs-extra": "^10.1.0", "git-branch-is": "^4.0.0", "glob": "^7.1.6", "http2-proxy": "^5.0.53", "husky": "^8.0.1", "jscpd": "^4.0.5", "jsdom": "^24.0.0", "less": "^3.12.2", "less-loader": "^7.0.1", "license-checker": "^25.0.1", "lint-staged": "^10.4.0", "maxmind": "^4.1.4", "mini-css-extract-plugin": "2.0.0", "mockjs": "^1.1.0", "node-fetch": "^2.6.1", "openai": "^5.10.1", "openapi-typescript": "7.0.0-next.2", "postcss": "^8.4.19", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.8.3", "process": "^0.11.10", "raw-loader": "^4.0.1", "react-dev-utils": "^12.0.1", "rspack-manifest-plugin": "^5.0.1", "sass": "^1.62.1", "sass-loader": "^12.6.0", "semver": "7.3.5", "svgo": "^1.3.2", "svgo-loader": "^2.2.1", "tailwindcss": "3.4.0", "textlint": "^14.4.2", "textlint-rule-en-capitalization": "^2.0.3", "textlint-rule-terminology": "^3.0.4", "textlint-rule-write-good": "^2.0.0", "timezone-mock": "^1.3.6", "tinify": "^1.7.1", "tinymce": "^6.4.2", "ts-morph": "^25.0.1", "tslib": "^2.6.2", "tsx": "^4.20.3", "typescript": "^5.8.3", "vitest": "^3.0.7", "yargs": "^15.4.1"}, "pnpm": {"overrides": {"rc-table@~7.11.0": "~7.22.2", "@swc/plugin-styled-components": "^6.8.2"}, "patchedDependencies": {"@fullcalendar/scrollgrid@5.9.0": "patches/@<EMAIL>", "@fullcalendar/interaction@5.9.0": "patches/@<EMAIL>", "@fullcalendar/common@5.9.0": "patches/@<EMAIL>", "antd@4.9.2": "patches/<EMAIL>", "@adyen/adyen-web@6.7.0": "patches/@<EMAIL>"}}, "engines": {"node": ">=16.0.0", "pnpm": "^8.0.0"}, "packageManager": "pnpm@8.15.7+sha256.50783dd0fa303852de2dd1557cd4b9f07cb5b018154a6e76d0f40635d6cee019"}